name: Deploy to EC2

on:
  workflow_run:
    workflows: [ "Test" ]  # Must match the name in test.yml
    types:
      - completed

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Build with gradle
        run: ./gradlew clean bootJar

      - name: Get version from Gradle
        id: version
        run: |
          VERSION=$(./gradlew properties | grep "^version:" | awk '{print $2}')
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        run: |
          docker build -t ghcr.io/baalismlin/helply-app:${{ steps.version.outputs.version }} \
                       -t ghcr.io/baalismlin/helply-app:latest \
                       --build-arg APP_VERSION=${{ steps.version.outputs.version }} \
                       ./app
                  

      - name: Save Docker image as tar
        run: docker save ghcr.io/baalismlin/helply-app:${{ steps.version.outputs.version }} -o helply-app.tar

      - name: Fix permissions on tar
        run: chmod 644 helply-app.tar

      - name: Remove Old Docker Image on EC2
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            rm -f helply-app.tar

      - name: Copy Docker image tar to EC2
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          source: "helply-app.tar"
          target: "/home/<USER>"

      - name: Restart app on EC2
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            docker load -i helply-app.tar
            docker stop helply-server-01 || true
            docker rm helply-server-01 || true
            docker run -d -e SPRING_PROFILES_ACTIVE=prod --name helply-server-01 --restart always -p 8080:8080 ghcr.io/baalismlin/helply-app:${{ steps.version.outputs.version }}