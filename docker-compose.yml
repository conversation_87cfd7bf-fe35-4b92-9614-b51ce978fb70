services:
  database:
    container_name: postgres-01
    image: postgres:17
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password123
      - POSTGRES_DB=e_commerce

  pgadmin:
    container_name: pgadmin-01
    image: dpage/pgadmin4:9.4
    restart: always
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=password123
    labels:
      traefik.http.routers.phpmyadmin.rule: Host(`db.localhost`)
      traefik.http.services.phpmyadmin.loadbalancer.server.port: 80

  app:
    container_name: app-01
    image: ghcr.io/baalismlin/helply-app:${APP_VERSION}
    build:
      context: ./app
      args:
        APP_VERSION: "${APP_VERSION}"
