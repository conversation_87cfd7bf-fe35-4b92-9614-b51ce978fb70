package org.helply.user.infrastructure.persistence.jpa;

import org.helply.user.domain.User;
import org.helply.shared.domain.Email;
import org.helply.user.domain.UserId;
import org.helply.user.infrastructure.persistence.UserRepositoryImpl;
import org.helply.user.infrastructure.persistence.mapper.UserEntityMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserRepositoryImplTest {

    @Mock
    private UserEntityMapper userEntityMapper;

    @Mock
    private UserJpaRepository userJpaRepository;

    private UserRepositoryImpl userRepository;

    @BeforeEach
    void setUp() {
        userRepository = new UserRepositoryImpl(userEntityMapper, userJpaRepository);
    }

    @Test
    void save_shouldSaveUserAndReturnMappedResult() {
        // Given
        User user = Instancio.of(User.class).create();
        UserJpaEntity userEntity = new UserJpaEntity();
        UserJpaEntity savedEntity = new UserJpaEntity();
        User savedUser = Instancio.of(User.class).create();

        when(userEntityMapper.toEntity(user)).thenReturn(userEntity);
        when(userJpaRepository.save(userEntity)).thenReturn(savedEntity);
        when(userEntityMapper.toDomain(savedEntity)).thenReturn(savedUser);

        // When
        User result = userRepository.save(user);

        // Then
        assertEquals(savedUser, result);
        verify(userEntityMapper).toEntity(user);
        verify(userJpaRepository).save(userEntity);
        verify(userEntityMapper).toDomain(savedEntity);
    }

    @Test
    void delete_shouldCallJpaRepositoryDeleteById() {
        // Given
        UserId userId = new UserId(1L);

        // When
        userRepository.delete(userId);

        // Then
        verify(userJpaRepository).deleteById(1L);
    }

    @Test
    void findById_shouldReturnUserWhenExists() {
        // Given
        UserId userId = new UserId(1L);
        UserJpaEntity userEntity = new UserJpaEntity();
        User expectedUser = Instancio.of(User.class).create();

        when(userJpaRepository.findById(1L)).thenReturn(Optional.of(userEntity));
        when(userEntityMapper.toDomain(userEntity)).thenReturn(expectedUser);

        // When
        Optional<User> result = userRepository.findById(userId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedUser, result.get());
        verify(userJpaRepository).findById(1L);
        verify(userEntityMapper).toDomain(userEntity);
    }

    @Test
    void findById_shouldReturnEmptyWhenNotExists() {
        // Given
        UserId userId = new UserId(1L);

        when(userJpaRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        Optional<User> result = userRepository.findById(userId);

        // Then
        assertFalse(result.isPresent());
        verify(userJpaRepository).findById(1L);
        verify(userEntityMapper, never()).toDomain(any());
    }

    @Test
    void findByIdWithAddresses_shouldReturnUserWhenExists() {
        // Given
        UserId userId = new UserId(1L);
        UserJpaEntity userEntity = new UserJpaEntity();
        User expectedUser = Instancio.of(User.class).create();

        when(userJpaRepository.findByIdWithAddresses(1L)).thenReturn(Optional.of(userEntity));
        when(userEntityMapper.toDomain(userEntity)).thenReturn(expectedUser);

        // When
        Optional<User> result = userRepository.findByIdWithAddresses(userId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedUser, result.get());
        verify(userJpaRepository).findByIdWithAddresses(1L);
        verify(userEntityMapper).toDomain(userEntity);
    }

    @Test
    void findByIdWithAddresses_shouldReturnEmptyWhenNotExists() {
        // Given
        UserId userId = new UserId(1L);

        when(userJpaRepository.findByIdWithAddresses(1L)).thenReturn(Optional.empty());

        // When
        Optional<User> result = userRepository.findByIdWithAddresses(userId);

        // Then
        assertFalse(result.isPresent());
        verify(userJpaRepository).findByIdWithAddresses(1L);
        verify(userEntityMapper, never()).toDomain(any());
    }

    @Test
    void findByEmail_shouldReturnUserWhenExists() {
        // Given
        Email email = new Email("<EMAIL>");
        UserJpaEntity userEntity = new UserJpaEntity();
        User expectedUser = Instancio.of(User.class).create();

        when(userJpaRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(userEntity));
        when(userEntityMapper.toDomain(userEntity)).thenReturn(expectedUser);

        // When
        Optional<User> result = userRepository.findByEmail(email);

        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedUser, result.get());
        verify(userJpaRepository).findByEmail("<EMAIL>");
        verify(userEntityMapper).toDomain(userEntity);
    }

    @Test
    void findByEmail_shouldReturnEmptyWhenNotExists() {
        // Given
        Email email = new Email("<EMAIL>");

        when(userJpaRepository.findByEmail("<EMAIL>")).thenReturn(Optional.empty());

        // When
        Optional<User> result = userRepository.findByEmail(email);

        // Then
        assertFalse(result.isPresent());
        verify(userJpaRepository).findByEmail("<EMAIL>");
        verify(userEntityMapper, never()).toDomain(any());
    }

    @Test
    void save_shouldHandleNullUserIdentity() {
        // Given
        User user = Instancio.of(User.class).create();
        UserJpaEntity userEntity = new UserJpaEntity();
        UserJpaEntity savedEntity = new UserJpaEntity();
        User savedUser = Instancio.of(User.class).create();

        when(userEntityMapper.toEntity(user)).thenReturn(userEntity);
        when(userJpaRepository.save(userEntity)).thenReturn(savedEntity);
        when(userEntityMapper.toDomain(savedEntity)).thenReturn(savedUser);

        // When
        User result = userRepository.save(user);

        // Then
        assertNotNull(result);
        verify(userEntityMapper).toEntity(user);
        verify(userJpaRepository).save(userEntity);
        verify(userEntityMapper).toDomain(savedEntity);
    }

}
