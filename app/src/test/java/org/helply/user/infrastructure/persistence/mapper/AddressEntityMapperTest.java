package org.helply.user.infrastructure.persistence.mapper;

import org.helply.user.domain.Address;
import org.helply.user.infrastructure.persistence.jpa.AddressJpaEntity;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class AddressEntityMapperTest {

    private AddressEntityMapper addressEntityMapper;

    @BeforeEach
    void setUp() {
        addressEntityMapper = new AddressEntityMapper();
    }

    @Test
    void toDomain_shouldConvertAddressJpaEntityToAddress() {
        // Given
        AddressJpaEntity entity = new AddressJpaEntity();
        entity.setId(1L);
        entity.setName("John Doe");
        entity.setPhoneNumber("1234567890");
        entity.setCountryCode("+1");

        // When
        Address result = addressEntityMapper.toDomain(entity);

        // Then
        assertNotNull(result);
        assertEquals("<PERSON>", result.getRecipient().getValue());
        assertEquals("1234567890", result.getPhoneNumber().getPhoneNumber());
        assertEquals("+1", result.getPhoneNumber().getCountryCode());
    }

    @Test
    void toEntity_shouldConvertAddressToAddressJpaEntity() {
        // Given
        Address address = Instancio.of(Address.class).create();

        // When
        AddressJpaEntity result = addressEntityMapper.toEntity(address);

        // Then
        assertNotNull(result);
        assertEquals(address.getId().getValue(), result.getId());
        assertEquals(address.getCountry(), result.getCountry());
        assertEquals(address.getState(), result.getState());
        assertEquals(address.getCity(), result.getCity());
        assertEquals(address.getPostcode(), result.getPostcode());
        assertEquals(address.getStreet(), result.getStreet());
        assertEquals(address.getRecipient().getValue(), result.getName());
        assertEquals(address.getPhoneNumber().getCountryCode(), result.getCountryCode());
        assertEquals(address.getPhoneNumber().getPhoneNumber(), result.getPhoneNumber());
        assertEquals(address.isBillAddress(), result.isBillAddress());
        assertEquals(address.isDefaultAddress(), result.isDefaultAddress());
    }
}
