package org.helply.user.infrastructure.persistence.mapper;

import org.helply.user.domain.Address;
import org.helply.user.domain.User;
import org.helply.shared.domain.Email;
import org.helply.user.infrastructure.persistence.jpa.AddressJpaEntity;
import org.helply.user.infrastructure.persistence.jpa.UserJpaEntity;
import org.helply.shared.infrastructure.util.SafeLazyLoader;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserEntityMapperTest {

    @Mock
    private AddressEntityMapper addressEntityMapper;

    @Mock
    private SafeLazyLoader safeLazyLoader;

    private UserEntityMapper userEntityMapper;

    @BeforeEach
    void setUp() {
        userEntityMapper = new UserEntityMapper(addressEntityMapper, safeLazyLoader);
    }

    @Test
    void toDomain_shouldConvertUserJpaEntityToUser() {
        // Given
        UserJpaEntity entity = new UserJpaEntity();
        entity.setId(1L);
        entity.setEmail("<EMAIL>");
        entity.setName("John Doe");
        entity.setBirthday(LocalDate.of(1990, 1, 1));
        entity.setLastModifyTime(LocalDateTime.now());
        entity.setAddresses(List.of());

        when(safeLazyLoader.isLoaded(entity, "addresses")).thenReturn(true);

        // When
        User result = userEntityMapper.toDomain(entity);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId().getValue());
        assertEquals("<EMAIL>", result.getEmail().getValue());
        assertEquals("John Doe", result.getName().getValue());
        assertEquals(LocalDate.of(1990, 1, 1), result.getBirthday());
        assertTrue(result.getAddresses().isEmpty());
    }

    @Test
    void toDomain_shouldHandleLazyLoadedAddresses() {
        // Given
        UserJpaEntity entity = new UserJpaEntity();
        entity.setId(1L);
        entity.setEmail("<EMAIL>");
        entity.setName("John Doe");
        entity.setBirthday(LocalDate.of(1990, 1, 1));
        entity.setLastModifyTime(LocalDateTime.now());

        AddressJpaEntity addressEntity = new AddressJpaEntity();
        entity.setAddresses(List.of(addressEntity));

        when(safeLazyLoader.isLoaded(entity, "addresses")).thenReturn(true);
        when(addressEntityMapper.toDomain(any(AddressJpaEntity.class)))
            .thenReturn(Instancio.of(Address.class).create());

        // When
        User result = userEntityMapper.toDomain(entity);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getAddresses().size());
    }

    @Test
    void toDomain_shouldHandleNotLoadedAddresses() {
        // Given
        UserJpaEntity entity = new UserJpaEntity();
        entity.setId(1L);
        entity.setEmail("<EMAIL>");
        entity.setName("John Doe");
        entity.setBirthday(LocalDate.of(1990, 1, 1));
        entity.setLastModifyTime(LocalDateTime.now());

        when(safeLazyLoader.isLoaded(entity, "addresses")).thenReturn(false);

        // When
        User result = userEntityMapper.toDomain(entity);

        // Then
        assertNotNull(result);
        assertTrue(result.getAddresses().isEmpty());
    }

    @Test
    void toEntity_shouldConvertUserToUserJpaEntity() {
        // Given
        User user = Instancio.of(User.class)
            .generate(field(User::getEmail).within(field(User::getEmail).toScope()),
                gen -> gen.emit().item(new Email("<EMAIL>"), 1))
            .create();


        when(addressEntityMapper.toEntity(any(Address.class)))
            .thenReturn(new AddressJpaEntity());

        // When
        UserJpaEntity result = userEntityMapper.toEntity(user);

        // Then
        assertNotNull(result);
        assertEquals(user.getId().getValue(), result.getId());
        assertEquals(user.getEmail().getValue(), result.getEmail());
        assertEquals(user.getName().getValue(), result.getName());
        assertEquals(user.getBirthday(), result.getBirthday());
        assertEquals(user.getAddresses().size(), result.getAddresses().size());
    }
}
