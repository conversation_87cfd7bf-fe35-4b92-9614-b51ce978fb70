package org.helply.user.domain;

import org.helply.shared.domain.PhoneNumber;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class AddressBuilderTest {

    @Test
    void build_shouldCreateAddressWithAllProperties() {
        // Given
        AddressId identity = new AddressId(1L);
        Name recipient = Name.of("<PERSON>", "<PERSON>", "<PERSON>e");
        PhoneNumber phoneNumber = new PhoneNumber("+1", "1234567890");
        String country = "USA";
        String state = "CA";
        String city = "San Francisco";
        String postcode = "94102";
        String street = "123 Main St";
        boolean defaultAddress = true;
        boolean billAddress = false;
        LocalDateTime lastModifyTime = LocalDateTime.now();

        // When
        Address address = AddressBuilder.anAddress()
            .withId(identity)
            .withRecipient(recipient)
            .withPhoneNumber(phoneNumber)
            .withCountry(country)
            .withState(state)
            .withCity(city)
            .withPostcode(postcode)
            .withStreet(street)
            .withDefaultAddress(defaultAddress)
            .withBillAddress(billAddress)
            .withLastModifyTime(lastModifyTime)
            .build();

        // Then
        assertNotNull(address);
        assertEquals(identity, address.getId());
        assertEquals(recipient, address.getRecipient());
        assertEquals(phoneNumber, address.getPhoneNumber());
        assertEquals(country, address.getCountry());
        assertEquals(state, address.getState());
        assertEquals(city, address.getCity());
        assertEquals(postcode, address.getPostcode());
        assertEquals(street, address.getStreet());
        assertTrue(address.isDefaultAddress());
        assertFalse(address.isBillAddress());
        assertEquals(lastModifyTime, address.getLastModifyTime());
    }

    @Test
    void build_shouldCreateAddressWithMinimalProperties() {
        // Given
        Name recipient = Name.of("John", "M", "Doe");
        PhoneNumber phoneNumber = new PhoneNumber("+1", "1234567890");
        String country = "USA";
        String state = "CA";
        String city = "San Francisco";
        String postcode = "94102";
        String street = "123 Main St";

        // When
        Address address = AddressBuilder.anAddress()
            .withRecipient(recipient)
            .withPhoneNumber(phoneNumber)
            .withCountry(country)
            .withState(state)
            .withCity(city)
            .withPostcode(postcode)
            .withStreet(street)
            .build();

        // Then
        assertNotNull(address);
        assertNull(address.getId());
        assertEquals(recipient, address.getRecipient());
        assertEquals(phoneNumber, address.getPhoneNumber());
        assertEquals(country, address.getCountry());
        assertEquals(state, address.getState());
        assertEquals(city, address.getCity());
        assertEquals(postcode, address.getPostcode());
        assertEquals(street, address.getStreet());
        assertFalse(address.isDefaultAddress());
        assertFalse(address.isBillAddress());
        assertNull(address.getLastModifyTime());
    }

    @Test
    void build_shouldAllowMethodChaining() {
        // Given & When
        Address address = AddressBuilder.anAddress()
            .withId(new AddressId(1L))
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .withDefaultAddress(true)
            .withBillAddress(false)
            .build();

        // Then
        assertNotNull(address);
        assertEquals(1L, address.getId().getValue());
        assertEquals("John M Doe", address.getRecipient().getValue());
        assertEquals("1234567890", address.getPhoneNumber().getPhoneNumber());
        assertEquals("+1", address.getPhoneNumber().getCountryCode());
        assertEquals("USA", address.getCountry());
        assertEquals("CA", address.getState());
        assertEquals("San Francisco", address.getCity());
        assertEquals("94102", address.getPostcode());
        assertEquals("123 Main St", address.getStreet());
        assertTrue(address.isDefaultAddress());
        assertFalse(address.isBillAddress());
    }

    @Test
    void anAddress_shouldReturnNewBuilderInstance() {
        // When
        AddressBuilder builder1 = AddressBuilder.anAddress();
        AddressBuilder builder2 = AddressBuilder.anAddress();

        // Then
        assertNotNull(builder1);
        assertNotNull(builder2);
        assertNotSame(builder1, builder2);
    }

    @Test
    void build_shouldSetDefaultValuesForBooleanFields() {
        // Given & When
        Address address = AddressBuilder.anAddress()
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .build();

        // Then
        assertFalse(address.isDefaultAddress());
        assertFalse(address.isBillAddress());
    }
}
