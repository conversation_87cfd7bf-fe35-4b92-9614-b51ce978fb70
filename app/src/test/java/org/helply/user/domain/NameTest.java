package org.helply.user.domain;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class NameTest {

    @Test
    void constructor_shouldCreateNameWithValidValue() {
        // Given
        String getValue = "<PERSON>";

        // When
        Name name = new Name(getValue);

        // Then
        assertNotNull(name);
        assertEquals(getValue, name.getValue());
    }

    @Test
    void constructor_shouldThrowExceptionWhenValueIsNull() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new Name(null));
    }

    @Test
    void constructor_shouldThrowExceptionWhenValueIsEmpty() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new Name(""));
    }

    @Test
    void constructor_shouldThrowExceptionWhenValueIsBlank() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new Name("   "));
    }

    @Test
    void of_shouldCreateNameFromSingleName() {
        // Given
        String firstName = "John";

        // When
        Name name = Name.of(firstName);

        // Then
        assertNotNull(name);
        assertEquals("John", name.getValue());
    }

    @Test
    void of_shouldCreateNameFromTwoNames() {
        // Given
        String firstName = "John";
        String lastName = "Doe";

        // When
        Name name = Name.of(firstName, lastName);

        // Then
        assertNotNull(name);
        assertEquals("John Doe", name.getValue());
    }

    @Test
    void of_shouldCreateNameFromThreeNames() {
        // Given
        String firstName = "John";
        String middleName = "Michael";
        String lastName = "Doe";

        // When
        Name name = Name.of(firstName, middleName, lastName);

        // Then
        assertNotNull(name);
        assertEquals("John Michael Doe", name.getValue());
    }

    @Test
    void of_shouldSkipNullNames() {
        // Given
        String firstName = "John";
        String middleName = null;
        String lastName = "Doe";

        // When
        Name name = Name.of(firstName, middleName, lastName);

        // Then
        assertNotNull(name);
        assertEquals("John Doe", name.getValue());
    }

    @Test
    void of_shouldHandleEmptyArray() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> Name.of());
    }

    @Test
    void of_shouldHandleMixedNullAndEmptyStrings() {
        // Given
        String firstName = "John";
        String middleName = "";
        String lastName = "Doe";

        // When
        Name name = Name.of(firstName, middleName, lastName);

        // Then
        assertNotNull(name);
        assertEquals("John  Doe", name.getValue());
    }

    @Test
    void equals_shouldReturnTrueForSameValue() {
        // Given
        Name name1 = new Name("John Doe");
        Name name2 = new Name("John Doe");

        // When & Then
        assertEquals(name1, name2);
        assertEquals(name1.hashCode(), name2.hashCode());
    }

    @Test
    void equals_shouldReturnFalseForDifferentValue() {
        // Given
        Name name1 = new Name("John Doe");
        Name name2 = new Name("Jane Smith");

        // When & Then
        assertNotEquals(name1, name2);
    }

    @Test
    void toString_shouldReturnCorrectFormat() {
        // Given
        Name name = new Name("John Doe");

        // When
        String result = name.toString();

        // Then
        assertTrue(result.contains("John Doe"));
    }
}
