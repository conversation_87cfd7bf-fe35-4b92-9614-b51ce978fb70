package org.helply.user.domain;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class UserIdTest {

    @Test
    void constructor_shouldCreateUserIdWithValidValue() {
        // Given
        Long value = 1L;

        // When
        UserId userId = new UserId(value);

        // Then
        assertNotNull(userId);
        assertEquals(value, userId.getValue());
    }

    @Test
    void constructor_shouldThrowExceptionWhenValueIsNull() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new UserId(null));
    }

    @Test
    void constructor_shouldAcceptZeroValue() {
        // Given
        Long value = 0L;

        // When
        UserId userId = new UserId(value);

        // Then
        assertNotNull(userId);
        assertEquals(value, userId.getValue());
    }

    @Test
    void constructor_shouldNotAcceptNegativeValue() {
        // Given
        Long value = -1L;

        // Then
        assertThrows(IllegalArgumentException.class, () -> new UserId(value));
    }

    @Test
    void constructor_shouldAcceptLargeValue() {
        // Given
        Long value = Long.MAX_VALUE;

        // When
        UserId userId = new UserId(value);

        // Then
        assertNotNull(userId);
        assertEquals(value, userId.getValue());
    }

    @Test
    void equals_shouldReturnTrueForSameValue() {
        // Given
        UserId userId1 = new UserId(1L);
        UserId userId2 = new UserId(1L);

        // When & Then
        assertEquals(userId1, userId2);
        assertEquals(userId1.hashCode(), userId2.hashCode());
    }

    @Test
    void equals_shouldReturnFalseForDifferentValue() {
        // Given
        UserId userId1 = new UserId(1L);
        UserId userId2 = new UserId(2L);

        // When & Then
        assertNotEquals(userId1, userId2);
    }

    @Test
    void toString_shouldReturnCorrectFormat() {
        // Given
        UserId userId = new UserId(123L);

        // When
        String result = userId.toString();

        // Then
        assertTrue(result.contains("123"));
    }
}
