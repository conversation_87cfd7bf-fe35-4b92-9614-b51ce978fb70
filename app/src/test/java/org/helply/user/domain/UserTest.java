package org.helply.user.domain;

import org.helply.user.exception.AddressAlreadyExistedException;
import org.instancio.Instancio;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.instancio.Select.field;

class UserTest {

    private User user;

    @BeforeEach
    void setup() {
        user = Instancio.of(User.class)
            .generate(field(User::getAddresses), gen -> gen.collection().size(1))
            .create();
    }

    @Test
    void addAddress() {
        var address = Instancio.of(Address.class).create();
        user.addAddress(address);
        Assertions.assertEquals(2, user.getAddresses().size());
    }

    @Test
    void addAddress_existed() {
        var seed = 32415L;
        var builder = Instancio.of(Address.class).withSeed(seed);
        var address1 = builder.create();
        var address2 = builder.create();
        user.addAddress(address1);

        Assertions.assertThrowsExactly(AddressAlreadyExistedException.class, () -> user.addAddress(address2));
    }

    @Test
    void deleteAddress() {
        var address = Instancio.of(Address.class).create();
        user.addAddress(address);

        user.deleteAddress(address.getId());

        Assertions.assertEquals(1, user.getAddresses().size());
    }

    @Test
    void deleteAllAddresses() {

        user.deleteAllAddresses();

        Assertions.assertEquals(0, user.getAddresses().size());
    }

}