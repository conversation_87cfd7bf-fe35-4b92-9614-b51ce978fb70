package org.helply.user.domain;

import org.helply.shared.domain.PhoneNumber;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class AddressTest {

    @Test
    void constructor_shouldCreateAddressWithValidParameters() {
        // Given
        AddressId identity = new AddressId(1L);
        Name recipient = Name.of("<PERSON>", "M", "Doe");
        PhoneNumber phoneNumber = new PhoneNumber("+1", "1234567890");
        String country = "USA";
        String state = "CA";
        String city = "San Francisco";
        String postcode = "94102";
        String street = "123 Main St";
        boolean defaultAddress = true;
        boolean billAddress = false;
        LocalDateTime lastModifyTime = LocalDateTime.now();

        // When
        Address address = new Address(identity, recipient, phoneNumber, country, state, city, postcode, street, defaultAddress, billAddress, lastModifyTime);

        // Then
        assertNotNull(address);
        assertEquals(identity, address.getId());
        assertEquals(recipient, address.getRecipient());
        assertEquals(phoneNumber, address.getPhoneNumber());
        assertEquals(country, address.getCountry());
        assertEquals(state, address.getState());
        assertEquals(city, address.getCity());
        assertEquals(postcode, address.getPostcode());
        assertEquals(street, address.getStreet());
        assertTrue(address.isDefaultAddress());
        assertFalse(address.isBillAddress());
        assertEquals(lastModifyTime, address.getLastModifyTime());
    }

    @Test
    void equals_shouldReturnTrueForSameIdentity() {
        // Given
        AddressId identity = new AddressId(1L);
        Address address1 = AddressBuilder.anAddress()
            .withId(identity)
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .build();

        Address address2 = AddressBuilder.anAddress()
            .withId(identity)
            .withRecipient(Name.of("Jane", "K", "Smith"))
            .withPhoneNumber(new PhoneNumber("+1", "0987654321"))
            .withCountry("Canada")
            .withState("ON")
            .withCity("Toronto")
            .withPostcode("M5V 3A8")
            .withStreet("456 Oak Ave")
            .build();

        // When & Then
        assertEquals(address1, address2);
        assertEquals(address1.hashCode(), address2.hashCode());
    }

    @Test
    void equals_shouldReturnFalseForDifferentIdentity() {
        // Given
        Address address1 = AddressBuilder.anAddress()
            .withId(new AddressId(1L))
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .build();

        Address address2 = AddressBuilder.anAddress()
            .withId(new AddressId(2L))
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .build();

        // When & Then
        assertNotEquals(address1, address2);
    }

    @Test
    void equals_shouldReturnFalseForDifferentType() {
        // Given
        Address address = AddressBuilder.anAddress()
            .withId(new AddressId(1L))
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .build();

        // When & Then
        assertNotEquals(address, "not an address");
        assertNotEquals(address, null);
    }

    @Test
    void equals_shouldReturnTrueForSameInstance() {
        // Given
        Address address = AddressBuilder.anAddress()
            .withId(new AddressId(1L))
            .withRecipient(Name.of("John", "M", "Doe"))
            .withPhoneNumber(new PhoneNumber("+1", "1234567890"))
            .withCountry("USA")
            .withState("CA")
            .withCity("San Francisco")
            .withPostcode("94102")
            .withStreet("123 Main St")
            .build();

        // When & Then
        assertEquals(address, address);
    }
}
