package org.helply.shared.domain;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class EmailTest {

    @Test
    void constructor_shouldCreateEmailWithValidValue() {
        // Given
        String emailValue = "<EMAIL>";

        // When
        Email email = new Email(emailValue);

        // Then
        assertNotNull(email);
        assertEquals(emailValue, email.getValue());
    }

    @Test
    void constructor_shouldNotAllowNullValue() {
        Assertions.assertThrows(NullPointerException.class, () -> new Email(null));
    }

    @Test
    void constructor_shouldNotAllowEmptyValue() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> new Email(""));
    }

    @Test
    void equals_shouldReturnTrueForSameValue() {
        // Given
        Email email1 = new Email("<EMAIL>");
        Email email2 = new Email("<EMAIL>");

        // When & Then
        assertEquals(email1, email2);
        assertEquals(email1.hashCode(), email2.hashCode());
    }

    @Test
    void equals_shouldReturnFalseForDifferentValue() {
        // Given
        Email email1 = new Email("<EMAIL>");
        Email email2 = new Email("<EMAIL>");

        // When & Then
        assertNotEquals(email1, email2);
    }

    @Test
    void toString_shouldReturnCorrectFormat() {
        // Given
        Email email = new Email("<EMAIL>");

        // When
        String result = email.toString();

        // Then
        assertTrue(result.contains("<EMAIL>"));
    }
}
