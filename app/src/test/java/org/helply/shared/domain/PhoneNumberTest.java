package org.helply.shared.domain;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class PhoneNumberTest {

    @Test
    void constructor_shouldCreatePhoneNumberWithValidValues() {
        // Given
        String phoneNumber = "1234567890";
        String countryCode = "+1";

        // When
        PhoneNumber phone = new PhoneNumber(countryCode, phoneNumber);

        // Then
        assertNotNull(phone);
        assertEquals(phoneNumber, phone.getPhoneNumber());
        assertEquals(countryCode, phone.getCountryCode());
    }

    @Test
    void constructor_shouldThrowExceptionWhenPhoneNumberIsNull() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber("+1", null));
    }

    @Test
    void constructor_shouldThrowExceptionWhenPhoneNumberIsEmpty() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber("+1", ""));
    }

    @Test
    void constructor_shouldThrowExceptionWhenPhoneNumberIsBlank() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber("+1", "   "));
    }

    @Test
    void constructor_shouldThrowExceptionWhenCountryCodeIsNull() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber(null, "1234567890"));
    }

    @Test
    void constructor_shouldThrowExceptionWhenCountryCodeIsEmpty() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber("", "1234567890"));
    }

    @Test
    void constructor_shouldThrowExceptionWhenCountryCodeIsBlank() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber("   ", "1234567890"));
    }

    @Test
    void constructor_shouldThrowExceptionWhenCountryCodeDoesNotStartWithPlusAndIsTooShort() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> new PhoneNumber("1", "1234567890"));
    }

    @Test
    void constructor_shouldAcceptCountryCodeWithoutPlusIfLongEnough() {
        // Given
        String phoneNumber = "1234567890";
        String countryCode = "001";

        // When
        PhoneNumber phone = new PhoneNumber(countryCode, phoneNumber);

        // Then
        assertNotNull(phone);
        assertEquals(phoneNumber, phone.getPhoneNumber());
        assertEquals(countryCode, phone.getCountryCode());
    }

    @Test
    void constructor_shouldAcceptCountryCodeWithPlus() {
        // Given
        String phoneNumber = "1234567890";
        String countryCode = "+1";

        // When
        PhoneNumber phone = new PhoneNumber(countryCode, phoneNumber);

        // Then
        assertNotNull(phone);
        assertEquals(phoneNumber, phone.getPhoneNumber());
        assertEquals(countryCode, phone.getCountryCode());
    }

    @Test
    void constructor_shouldAcceptLongerCountryCodes() {
        // Given
        String phoneNumber = "1234567890";
        String countryCode = "+123";

        // When
        PhoneNumber phone = new PhoneNumber(countryCode, phoneNumber);

        // Then
        assertNotNull(phone);
        assertEquals(phoneNumber, phone.getPhoneNumber());
        assertEquals(countryCode, phone.getCountryCode());
    }

    @Test
    void constructor_shouldAcceptDifferentPhoneNumberFormats() {
        // Given
        String phoneNumber = "************";
        String countryCode = "+1";

        // When
        PhoneNumber phone = new PhoneNumber(countryCode, phoneNumber);

        // Then
        assertNotNull(phone);
        assertEquals(phoneNumber, phone.getPhoneNumber());
        assertEquals(countryCode, phone.getCountryCode());
    }

    @Test
    void equals_shouldReturnTrueForSameValues() {
        // Given
        PhoneNumber phone1 = new PhoneNumber("+1", "1234567890");
        PhoneNumber phone2 = new PhoneNumber("+1", "1234567890");

        // When & Then
        assertEquals(phone1, phone2);
        assertEquals(phone1.hashCode(), phone2.hashCode());
    }

    @Test
    void equals_shouldReturnFalseForDifferentPhoneNumbers() {
        // Given
        PhoneNumber phone1 = new PhoneNumber("+1", "1234567890");
        PhoneNumber phone2 = new PhoneNumber("+1", "0987654321");

        // When & Then
        assertNotEquals(phone1, phone2);
    }

    @Test
    void equals_shouldReturnFalseForDifferentCountryCodes() {
        // Given
        PhoneNumber phone1 = new PhoneNumber("+1", "1234567890");
        PhoneNumber phone2 = new PhoneNumber("+44", "1234567890");

        // When & Then
        assertNotEquals(phone1, phone2);
    }

    @Test
    void toString_shouldReturnCorrectFormat() {
        // Given
        PhoneNumber phone = new PhoneNumber("+1", "1234567890");

        // When
        String result = phone.toString();

        // Then
        assertTrue(result.contains("1234567890"));
        assertTrue(result.contains("+1"));
    }
}
