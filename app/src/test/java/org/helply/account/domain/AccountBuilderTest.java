package org.helply.account.domain;

import org.helply.shared.domain.Email;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class AccountBuilderTest {

    @Test
    void build_shouldCreateAccountWithAllProperties() {
        // Given
        AccountId identity = new AccountId(1L);
        Email email = new Email("<EMAIL>");
        Password password = Password.of("Password_123");
        AuthProvider provider = AuthProvider.LOCAL;
        LocalDateTime lastLoginTime = LocalDateTime.now();
        LocalDateTime lastModifyTime = LocalDateTime.now();

        // When
        Account account = AccountBuilder.anAccount()
            .withId(identity)
            .withEmail(email)
            .withPassword(password)
            .withProvider(provider)
            .withLastLoginTime(lastLoginTime)
            .withLastModifyTime(lastModifyTime)
            .build();

        // Then
        assertNotNull(account);
        assertEquals(identity, account.getId());
        assertEquals(email, account.getEmail());
        assertEquals(password, account.getPassword());
        assertEquals(provider, account.getProvider());
        assertEquals(lastLoginTime, account.getLastLoginTime());
        assertEquals(lastModifyTime, account.getLastModifyTime());
    }

    @Test
    void build_shouldCreateAccountWithMinimalProperties() {
        // Given
        Email email = new Email("<EMAIL>");
        Password password = Password.of("Password_123");
        AuthProvider provider = AuthProvider.LOCAL;

        // When
        Account account = AccountBuilder.anAccount()
            .withEmail(email)
            .withPassword(password)
            .withProvider(provider)
            .build();

        // Then
        assertNotNull(account);
        assertNull(account.getId());
        assertEquals(email, account.getEmail());
        assertEquals(password, account.getPassword());
        assertEquals(provider, account.getProvider());
        assertNull(account.getLastLoginTime());
        assertNull(account.getLastModifyTime());
    }

    @Test
    void build_shouldAllowMethodChaining() {
        // Given & When
        Account account = AccountBuilder.anAccount()
            .withId(new AccountId(1L))
            .withEmail(new Email("<EMAIL>"))
            .withPassword(Password.of("Password_123"))
            .withProvider(AuthProvider.GOOGLE)
            .withLastLoginTime(LocalDateTime.now())
            .withLastModifyTime(LocalDateTime.now())
            .build();

        // Then
        assertNotNull(account);
        assertEquals(1L, account.getId().getValue());
        assertEquals("<EMAIL>", account.getEmail().getValue());
        assertEquals(AuthProvider.GOOGLE, account.getProvider());
    }

    @Test
    void anAccount_shouldReturnNewBuilderInstance() {
        // When
        AccountBuilder builder1 = AccountBuilder.anAccount();
        AccountBuilder builder2 = AccountBuilder.anAccount();

        // Then
        assertNotNull(builder1);
        assertNotNull(builder2);
        assertNotSame(builder1, builder2);
    }
}
