package org.helply.account.infrastructure.security;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Encoders;
import org.helply.account.domain.Authenticator;
import org.helply.shared.domain.Email;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.env.MockEnvironment;

import java.security.KeyPair;

class JwtAuthenticatorTest {

    MockEnvironment mockEnvironment;
    JwtAuthenticator jwtAuthenticator;

    @BeforeEach
    void setup() {
        KeyPair keyPair = Jwts.SIG.EdDSA.keyPair().build();
        mockEnvironment = new MockEnvironment()
            .withProperty(Authenticator.PRIVATE_KEY_VALUE, Encoders.BASE64.encode(keyPair.getPrivate().getEncoded()))
            .withProperty(Authenticator.PUBLIC_KEY_VALUE, Encoders.BASE64.encode(keyPair.getPublic().getEncoded()))
            .withProperty(Authenticator.EXPIRATION, "2000");
        jwtAuthenticator = new JwtAuthenticator(mockEnvironment);
    }

    @Test
    void token() {
        Email email = new Email("<EMAIL>");
        String token = jwtAuthenticator.token(email);
        Assertions.assertNotNull(token);
    }

    @Test
    void verify() {
        Email email = new Email("<EMAIL>");
        String token = jwtAuthenticator.token(email);

        boolean verified = jwtAuthenticator.verify(token);

        Assertions.assertTrue(verified);
    }

    @Test
    void verify_invalid() {
        String token = " ";

        boolean verified = jwtAuthenticator.verify(token);

        Assertions.assertFalse(verified);
    }

    @Test
    void verify_expired() {
        Email email = new Email("<EMAIL>");
        String token = jwtAuthenticator.token(email);

        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        boolean verified = jwtAuthenticator.verify(token);

        Assertions.assertFalse(verified);
    }

    @Test
    void getEmailFromToken() {
        Email email = new Email("<EMAIL>");
        String token = jwtAuthenticator.token(email);

        Email resolvedEmail = jwtAuthenticator.getEmailFromToken(token);
        Assertions.assertEquals(email, resolvedEmail);
    }
}