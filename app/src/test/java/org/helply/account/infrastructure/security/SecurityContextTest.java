package org.helply.account.infrastructure.security;

import org.helply.account.domain.*;
import org.helply.shared.domain.Email;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SecurityContextTest {

    @Test
    void constructor_shouldCreateSecurityContextWithAccount() {
        // Given
        Account account = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();

        // When
        SecurityContext securityContext = new SecurityContext(account);

        // Then
        assertNotNull(securityContext);
        assertEquals(account, securityContext.getAccount());
    }

    @Test
    void anonymous_shouldCreateAnonymousSecurityContext() {
        // When
        SecurityContext anonymousContext = SecurityContext.anonymous();

        // Then
        assertNotNull(anonymousContext);
        assertNotNull(anonymousContext.getAccount());
        assertEquals("<EMAIL>", anonymousContext.getAccount().getEmail().getValue());
        assertEquals(AuthProvider.LOCAL, anonymousContext.getAccount().getProvider());
    }

    @Test
    void anonymous_shouldCreateNewInstanceEachTime() {
        // When
        SecurityContext context1 = SecurityContext.anonymous();
        SecurityContext context2 = SecurityContext.anonymous();

        // Then
        assertNotNull(context1);
        assertNotNull(context2);
        assertNotSame(context1, context2);
        // But they should be equal in content
        assertEquals(context1.getAccount().getEmail(), context2.getAccount().getEmail());
        assertEquals(context1.getAccount().getPassword(), context2.getAccount().getPassword());
        assertEquals(context1.getAccount().getProvider(), context2.getAccount().getProvider());
    }

    @Test
    void equals_shouldReturnFalseForDifferentAccounts() {
        // Given
        Account account1 = AccountBuilder.anAccount()
            .withId(new AccountId(1L))
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();

        Account account2 = AccountBuilder.anAccount()
            .withId(new AccountId(2L))
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();

        SecurityContext context1 = new SecurityContext(account1);
        SecurityContext context2 = new SecurityContext(account2);

        // When & Then
        assertNotEquals(context1, context2);
    }

    @Test
    void toString_shouldReturnCorrectFormat() {
        // Given
        Account account = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();

        SecurityContext securityContext = new SecurityContext(account);

        // When
        String result = securityContext.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("SecurityContext"));
    }
}
