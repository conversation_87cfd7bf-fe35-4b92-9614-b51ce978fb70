package org.helply.account.infrastructure.security;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountBuilder;
import org.helply.account.domain.AuthProvider;
import org.helply.shared.domain.Email;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SecurityContextHolderTest {

    @BeforeEach
    void setUp() {
        // Clear the security context before each test
        SecurityContextHolder.setContext(SecurityContext.anonymous());
    }

    @Test
    void getContext_shouldReturnAnonymousContextWhenNotSet() {
        // Given - context is cleared in setUp

        // When
        SecurityContext context = SecurityContextHolder.getContext();

        // Then
        assertNotNull(context);
        assertEquals("<EMAIL>", context.getAccount().getEmail().getValue());
        assertEquals(AuthProvider.LOCAL, context.getAccount().getProvider());
    }

    @Test
    void setContext_shouldSetSecurityContext() {
        // Given
        Account account = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();
        SecurityContext customContext = new SecurityContext(account);

        // When
        SecurityContextHolder.setContext(customContext);

        // Then
        SecurityContext retrievedContext = SecurityContextHolder.getContext();
        assertEquals(customContext, retrievedContext);
        assertEquals("<EMAIL>", retrievedContext.getAccount().getEmail().getValue());
    }

    @Test
    void setContext_shouldThrowExceptionWhenContextIsNull() {
        // When & Then
        assertThrows(NullPointerException.class, () -> SecurityContextHolder.setContext(null));
    }

    @Test
    void getContext_shouldReturnSameInstanceAfterSet() {
        // Given
        Account account = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();
        SecurityContext customContext = new SecurityContext(account);

        // When
        SecurityContextHolder.setContext(customContext);
        SecurityContext retrievedContext1 = SecurityContextHolder.getContext();
        SecurityContext retrievedContext2 = SecurityContextHolder.getContext();

        // Then
        assertSame(retrievedContext1, retrievedContext2);
        assertEquals(customContext, retrievedContext1);
    }

    @Test
    void setContext_shouldOverridePreviousContext() {
        // Given
        Account account1 = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();
        SecurityContext context1 = new SecurityContext(account1);

        Account account2 = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.GOOGLE)
            .build();
        SecurityContext context2 = new SecurityContext(account2);

        // When
        SecurityContextHolder.setContext(context1);
        SecurityContext retrievedContext1 = SecurityContextHolder.getContext();

        SecurityContextHolder.setContext(context2);
        SecurityContext retrievedContext2 = SecurityContextHolder.getContext();

        // Then
        assertEquals(context1, retrievedContext1);
        assertEquals("<EMAIL>", retrievedContext1.getAccount().getEmail().getValue());

        assertEquals(context2, retrievedContext2);
        assertEquals("<EMAIL>", retrievedContext2.getAccount().getEmail().getValue());
        assertEquals(AuthProvider.GOOGLE, retrievedContext2.getAccount().getProvider());
    }

    @Test
    void contextHolder_shouldBeThreadLocal() throws InterruptedException {
        // Given
        Account account1 = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();
        SecurityContext context1 = new SecurityContext(account1);

        Account account2 = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.GOOGLE)
            .build();
        SecurityContext context2 = new SecurityContext(account2);

        // When
        SecurityContextHolder.setContext(context1);

        Thread thread = new Thread(() -> {
            SecurityContextHolder.setContext(context2);
            SecurityContext threadContext = SecurityContextHolder.getContext();
            assertEquals("<EMAIL>", threadContext.getAccount().getEmail().getValue());
        });

        thread.start();
        thread.join();

        // Then
        SecurityContext mainThreadContext = SecurityContextHolder.getContext();
        assertEquals("<EMAIL>", mainThreadContext.getAccount().getEmail().getValue());
    }
}
