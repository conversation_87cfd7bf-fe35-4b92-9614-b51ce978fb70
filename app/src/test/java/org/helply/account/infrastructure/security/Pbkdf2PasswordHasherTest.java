package org.helply.account.infrastructure.security;

import org.helply.account.domain.Password;
import org.helply.account.domain.PlainPassword;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class Pbkdf2PasswordHasherTest {

    Pbkdf2PasswordHasher passwordHasher;

    @BeforeEach
    void setUp() {
        passwordHasher = new Pbkdf2PasswordHasher();
    }

    @Test
    void hash() {
        PlainPassword plainPassword = PlainPassword.of("123@JSsad_s");
        Password password = passwordHasher.hash(plainPassword);
        Assertions.assertTrue(password.getValue().contains("$"));
    }

    @Test
    void matches() {
        PlainPassword plainPassword = PlainPassword.of("123@JSsad_s");
        Password password = Password.of("1PNJf13MzHn+D8RnmfbcUg==$+ZNvvEEEnShsPBdv3jo2rPu03nL/bFNkrJ9ACuyyJew=");

        boolean matches = passwordHasher.matches(plainPassword, password);
        Assertions.assertTrue(matches);
    }

    @Test
    void matches_invalid() {
        PlainPassword plainPassword = PlainPassword.of("JSsad_s123@");
        Password password = Password.of("4k+smc+stgW66c39GMyufQ==$PEAzHK5eRV8lcGSj9vzQnB2m1Mu0Gb7t51m9wQEl4yc=");

        boolean matches = passwordHasher.matches(plainPassword, password);
        Assertions.assertFalse(matches);
    }
}