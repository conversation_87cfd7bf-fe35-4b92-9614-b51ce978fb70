package org.helply.account.application;

import org.helply.account.application.command.LocalLoginCommand;
import org.helply.account.application.query.AccessTokenDto;
import org.helply.account.domain.*;
import org.helply.account.exception.PasswordNotMatchedException;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.account.domain.Account;
import org.helply.shared.domain.Email;
import org.helply.account.infrastructure.security.GoogleTokenVerifier;
import org.helply.user.domain.UserRepository;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthenticationServiceTest {
    @Mock
    private AccountRepository accountRepository;

    @Mock
    private PasswordHasher passwordHasher;

    @Mock
    private Authenticator authenticator;

    @Mock
    private GoogleTokenVerifier googleTokenVerifier;
    
    @Mock
    private UserRepository userRepository;

    private AuthenticationService authenticationService;

    @BeforeEach
    void setUp() {
        authenticationService = new AuthenticationService(accountRepository, passwordHasher, authenticator, googleTokenVerifier, userRepository);
    }

    @Test
    void login_shouldReturnTokenWhenCredentialsAreValid() {
        // Given
        Email email = new Email("<EMAIL>");
        PlainPassword plainPassword = PlainPassword.of("Password_123");
        Password password = Password.of("hashed_password");
        String expectedToken = "jwt_token";

        Account account = Instancio.of(Account.class)
            .set(field(Account::getPassword), password)
            .set(field(Account::getEmail), email)
            .create();

        when(accountRepository.findByEmail(email)).thenReturn(Optional.of(account));
        when(passwordHasher.matches(plainPassword, password)).thenReturn(true);
        when(authenticator.token(email)).thenReturn(expectedToken);

        LocalLoginCommand command = new LocalLoginCommand();
        command.setEmail(email.getValue());
        command.setPassword(plainPassword.getValue());
        // When
        AccessTokenDto result = authenticationService.login(command);

        // Then
        assertEquals(expectedToken, result.getToken());
        verify(accountRepository).findByEmail(email);
        verify(passwordHasher).matches(plainPassword, password);
        verify(authenticator).token(email);
    }

    @Test
    void login_shouldThrowExceptionWhenPasswordDoesNotMatch() {
        // Given
        Email email = new Email("<EMAIL>");
        PlainPassword plainPassword = PlainPassword.of("Wrong_Password!23");
        Password password = Password.of("hashed_password");

        Account account = Instancio.of(Account.class)
            .set(field(Account::getEmail), email)
            .set(field(Account::getPassword), password)
            .create();

        LocalLoginCommand command = new LocalLoginCommand();
        command.setEmail(email.getValue());
        command.setPassword(plainPassword.getValue());

        when(accountRepository.findByEmail(email)).thenReturn(Optional.of(account));
        when(passwordHasher.matches(plainPassword, password)).thenReturn(false);
        
        // When & Then
        assertThrows(PasswordNotMatchedException.class, () -> authenticationService.login(command));
        verify(accountRepository).findByEmail(email);
        verify(passwordHasher).matches(plainPassword, password);
        verify(authenticator, never()).token(any());
    }

    @Test
    void login_shouldThrowExceptionWhenAccountNotFound() {
        // Given
        Email email = new Email("<EMAIL>");
        PlainPassword plainPassword = PlainPassword.of("Password_123");
        LocalLoginCommand command = new LocalLoginCommand();
        command.setEmail(email.getValue());
        command.setPassword(plainPassword.getValue());
        
        when(accountRepository.findByEmail(email)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(AccountNotFoundException.class, () -> authenticationService.login(command));
        verify(accountRepository).findByEmail(email);
        verify(passwordHasher, never()).matches(any(), any());
        verify(authenticator, never()).token(any());
    }
}