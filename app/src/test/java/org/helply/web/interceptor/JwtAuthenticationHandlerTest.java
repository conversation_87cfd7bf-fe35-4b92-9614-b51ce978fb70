package org.helply.web.interceptor;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountRepository;
import org.helply.user.domain.User;
import org.helply.account.domain.Authenticator;
import org.helply.account.infrastructure.security.SecurityContext;
import org.helply.account.infrastructure.security.SecurityContextHolder;
import org.helply.shared.domain.Email;
import org.helply.web.handler.JwtAuthenticationHandler;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JwtAuthenticationHandlerTest {

    @Mock
    private Authenticator authenticator;

    @Mock
    private AccountRepository accountRepository;

    private JwtAuthenticationHandler interceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() throws Exception {
        interceptor = new JwtAuthenticationHandler(authenticator, accountRepository);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();

        // Clear security context before each test
        SecurityContextHolder.setContext(SecurityContext.anonymous());
    }

    @Test
    void preHandle_shouldReturnTrueWhenValidJwtProvided() throws Exception {
        // Given
        String validJwt = "valid.jwt.token";
        Email email = new Email("<EMAIL>");
        Account account = Instancio.of(Account.class).create();

        request.addHeader("Authorization", "Bearer " + validJwt);

        when(authenticator.verify(validJwt)).thenReturn(true);
        when(authenticator.getEmailFromToken(validJwt)).thenReturn(email);
        when(accountRepository.findByEmail(email)).thenReturn(Optional.of(account));

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertTrue(result);
        verify(authenticator).verify(validJwt);
        verify(authenticator).getEmailFromToken(validJwt);
        verify(accountRepository).findByEmail(email);
        assertEquals(200, response.getStatus());
    }

    @Test
    void preHandle_shouldReturnFalseWhenNoAuthorizationHeader() throws Exception {
        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result);
        assertEquals(401, response.getStatus());
        assertEquals("application/json", response.getContentType());
        assertTrue(response.getContentAsString().contains("Unauthorized"));
        verify(authenticator, never()).verify(any());
    }

    @Test
    void preHandle_shouldReturnFalseWhenAuthorizationHeaderDoesNotStartWithBearer() throws Exception {
        // Given
        request.addHeader("Authorization", "Basic sometoken");

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result);
        assertEquals(401, response.getStatus());
        assertEquals("application/json", response.getContentType());
        assertTrue(response.getContentAsString().contains("Unauthorized"));
        verify(authenticator, never()).verify(any());
    }

    @Test
    void preHandle_shouldReturnFalseWhenJwtIsInvalid() throws Exception {
        // Given
        String invalidJwt = "invalid.jwt.token";
        request.addHeader("Authorization", "Bearer " + invalidJwt);

        when(authenticator.verify(invalidJwt)).thenReturn(false);

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result);
        assertEquals(401, response.getStatus());
        assertEquals("application/json", response.getContentType());
        assertTrue(response.getContentAsString().contains("Unauthorized"));
        verify(authenticator).verify(invalidJwt);
        verify(authenticator, never()).getEmailFromToken(any());
        verify(accountRepository, never()).findByEmail(any());
    }

    @Test
    void preHandle_shouldReturnFalseWhenJwtIsEmpty() throws Exception {
        // Given
        request.addHeader("Authorization", "Bearer ");

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result);
        assertEquals(401, response.getStatus());
        assertEquals("application/json", response.getContentType());
        assertTrue(response.getContentAsString().contains("Unauthorized"));
        verify(authenticator, never()).verify(any());
    }

    @Test
    void preHandle_shouldReturnFalseWhenAuthenticatorThrowsException() throws Exception {
        // Given
        String validJwt = "valid.jwt.token";
        request.addHeader("Authorization", "Bearer " + validJwt);

        when(authenticator.verify(validJwt)).thenThrow(new RuntimeException("JWT processing error"));

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result);
        assertEquals(401, response.getStatus());
        assertEquals("application/json", response.getContentType());
        assertTrue(response.getContentAsString().contains("Unauthorized"));
        verify(authenticator).verify(validJwt);
        verify(authenticator, never()).getEmailFromToken(any());
        verify(accountRepository, never()).findByEmail(any());
    }

    @Test
    void preHandle_shouldReturnFalseWhenUserServiceThrowsException() throws Exception {
        // Given
        String validJwt = "valid.jwt.token";
        Email email = new Email("<EMAIL>");
        request.addHeader("Authorization", "Bearer " + validJwt);

        when(authenticator.verify(validJwt)).thenReturn(true);
        when(authenticator.getEmailFromToken(validJwt)).thenReturn(email);
        when(accountRepository.findByEmail(email)).thenThrow(new RuntimeException("User not found"));

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result);
        assertEquals(401, response.getStatus());
        assertEquals("application/json", response.getContentType());
        assertTrue(response.getContentAsString().contains("Unauthorized"));
        verify(authenticator).verify(validJwt);
        verify(authenticator).getEmailFromToken(validJwt);
        verify(accountRepository).findByEmail(email);
    }

    @Test
    void preHandle_shouldHandleAuthorizationHeaderWithExtraSpaces() throws Exception {
        // Given
        String validJwt = "valid.jwt.token";
        Email email = new Email("<EMAIL>");
        User user = Instancio.of(User.class).create();

        request.addHeader("Authorization", "  Bearer   " + validJwt + "  ");

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertFalse(result); // Should fail because of extra spaces in token extraction
        assertEquals(401, response.getStatus());
    }

    @Test
    void preHandle_shouldExtractJwtCorrectlyFromBearerToken() throws Exception {
        // Given
        String validJwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
        Email email = new Email("<EMAIL>");
        Account account = Instancio.of(Account.class).create();

        request.addHeader("Authorization", "Bearer " + validJwt);

        when(authenticator.verify(validJwt)).thenReturn(true);
        when(authenticator.getEmailFromToken(validJwt)).thenReturn(email);
        when(accountRepository.findByEmail(email)).thenReturn(Optional.of(account));

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertTrue(result);
        verify(authenticator).verify(validJwt);
    }
}
