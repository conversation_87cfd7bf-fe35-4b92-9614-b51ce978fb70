package org.helply.chat.application;

import org.helply.chat.application.command.CreateMessageCommand;
import org.helply.chat.domain.*;
import org.helply.chat.infrastructure.ChatRepositoryStub;
import org.helply.user.application.UserQueryService;
import org.helply.user.domain.UserId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChatServiceTest {

    @Mock
    private SimpMessagingTemplate messagingTemplate;
    
    private ChatRepository chatRepository;
    private OnlineUserService onlineUserService;
    private ChatService chatService;

    @BeforeEach
    void setUp() {
        // Use the stub implementation for testing
        chatRepository = new ChatRepositoryStub();
        onlineUserService = new OnlineUserService(messagingTemplate);
        
        chatService = new ChatService(chatRepository, onlineUserService);
    }

    @Test
    void createMessage_shouldSaveMessageAndSendToOnlineParticipants() {
        // Given
        CreateMessageCommand command = new CreateMessageCommand();
        command.setConversationId(1L);
        command.setSenderId(1L);
        command.setContent("Hello, World!");
        command.setType(MessageType.TEXT);
        
        // Mark user 2 as online
        UserId user2 = UserId.of(2L);
        onlineUserService.markUserOnline(user2);
        
        // When
        assertDoesNotThrow(() -> chatService.createMessage(command));
        
        // Then
        // Verify that message was sent to online user
        verify(messagingTemplate).convertAndSendToUser(
            eq("2"), 
            eq("/queue/messages"), 
            any()
        );
        
        // Verify that message was not sent to sender (user 1)
        verify(messagingTemplate, never()).convertAndSendToUser(
            eq("1"), 
            anyString(), 
            any()
        );
    }

    @Test
    void createMessage_shouldHandleOfflineParticipants() {
        // Given
        CreateMessageCommand command = new CreateMessageCommand();
        command.setConversationId(1L);
        command.setSenderId(1L);
        command.setContent("Hello, World!");
        command.setType(MessageType.TEXT);
        
        // User 2 is offline (not marked as online)
        
        // When
        assertDoesNotThrow(() -> chatService.createMessage(command));
        
        // Then
        // Verify that no message was sent via WebSocket
        verify(messagingTemplate, never()).convertAndSendToUser(
            anyString(), 
            anyString(), 
            any()
        );
    }

    @Test
    void createMessage_shouldThrowExceptionForNonExistentChat() {
        // Given
        CreateMessageCommand command = new CreateMessageCommand();
        command.setConversationId(999L); // Non-existent chat
        command.setSenderId(1L);
        command.setContent("Hello, World!");
        command.setType(MessageType.TEXT);
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> chatService.createMessage(command));
        
        assertTrue(exception.getMessage().contains("Failed to create message"));
    }
}
