server:
  servlet:
    context-path: /api
  port: 8080

spring:
  profiles:
    active: dev
  application:
    name: helply
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  flyway:
    enabled: true
    validate-on-migrate: true
    baseline-on-migrate: true
    baseline-version: 0


# JWT Configuration
jwt:
  asymmetric:
    private: MEcCAQAwBQYDK2VxBDsEOU2Xqvl6MXf0RHf/+ykO+O9ThUSbynpML5l4WgwP6wquwEj06UVgbdWAF/DsPsjaNjbJHmcD/1L/6w==
    public: MEMwBQYDK2VxAzoAqesjAp4Zt4/rRxwhab2fRVXKJ/hiwIoNZB3i/vEozmqN9QMbsItQBWuidzhEAuymzMBSpQ/5vtEA
  expiration: 86400000 # 24 hours in milliseconds
