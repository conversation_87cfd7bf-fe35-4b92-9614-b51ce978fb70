server:
    servlet:
        context-path: /api
    port: 8080

spring:
    application:
        name: helply
    
    # H2 Database Configuration
    datasource:
        url: jdbc:h2:mem:testdb
        driver-class-name: org.h2.Driver
        username: sa
        password: value
    
    h2:
        console:
            enabled: true
            path: /h2-console
    
    jpa:
        database-platform: org.hibernate.dialect.H2Dialect
        hibernate:
            ddl-auto: create-drop
        show-sql: true
        properties:
            hibernate:
                format_sql: true


# JWT Configuration
jwt:
    asymmetric:
        private: MEcCAQAwBQYDK2VxBDsEOU2Xqvl6MXf0RHf/+ykO+O9ThUSbynpML5l4WgwP6wquwEj06UVgbdWAF/DsPsjaNjbJHmcD/1L/6w==
        public: MEMwBQYDK2VxAzoAqesjAp4Zt4/rRxwhab2fRVXKJ/hiwIoNZB3i/vEozmqN9QMbsItQBWuidzhEAuymzMBSpQ/5vtEA
    expiration: 86400000 # 24 hours in milliseconds
