
CREATE TABLE IF NOT EXISTS users
(
    id BIGSERIAL PRIMARY KEY,
    birthday date,
    create_time timestamp(6) without time zone,
    email character varying(255)  NOT NULL,
    last_modify_time timestamp(6) without time zone,
    name character varying(255) ,
    CONSTRAINT users_u_email UNIQUE (email)
);

CREATE TABLE IF NOT EXISTS accounts
(
    id BIGSERIAL PRIMARY KEY,
    create_time timestamp(6) without time zone,
    email character varying(255)  NOT NULL,
    last_login_time timestamp(6) without time zone,
    password character varying(255) ,
    provider character varying(255) ,
    user_id bigint,
    CONSTRAINT accounts_u_email UNIQUE (email),
    CONSTRAINT accounts_f_user_id FOREIGN KEY (user_id)
        REFERENCES users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT accounts_provider_check CHECK (provider::text = ANY (ARRAY['LOCAL'::character varying, 'GOOGLE'::character varying, 'FACEBOOK'::character varying]::text[]))
);

CREATE TABLE IF NOT EXISTS addresses
(
    id BIGSERIAL PRIMARY KEY,
    bill_address boolean NOT NULL,
    city character varying(255) ,
    country character varying(255) ,
    country_code character varying(255) ,
    default_address boolean NOT NULL,
    last_modify_time timestamp(6) without time zone,
    name character varying(255) ,
    phone_number character varying(255) ,
    postcode character varying(255) ,
    state character varying(255) ,
    street character varying(255) ,
    user_id bigint,
    CONSTRAINT addresses_f_user_id FOREIGN KEY (user_id)
        REFERENCES users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);


CREATE TABLE IF NOT EXISTS tasks
(
    id BIGSERIAL PRIMARY KEY,
    category character varying(255)  NOT NULL,
    description text ,
    last_modify_time timestamp(6) without time zone,
    price numeric(10,2),
    title character varying(255)  NOT NULL,
    user_id bigint,
    CONSTRAINT tasks_f_user_id FOREIGN KEY (user_id)
        REFERENCES users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT tasks_category_check CHECK (category::text = ANY (ARRAY['PET_CARE'::character varying, 'DRIVING'::character varying, 'ERRAND'::character varying, 'PHOTOGRAPHY'::character varying, 'DESIGN'::character varying, 'WRITING'::character varying, 'TRANSLATION'::character varying, 'REMOTE_ASSISTANCE'::character varying, 'OTHER'::character varying]::text[]))
);



