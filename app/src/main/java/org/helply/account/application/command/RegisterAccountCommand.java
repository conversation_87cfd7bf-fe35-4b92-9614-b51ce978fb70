package org.helply.account.application.command;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountBuilder;
import org.helply.account.domain.AuthProvider;
import org.helply.account.domain.Password;
import org.helply.shared.domain.Email;

public class RegisterAccountCommand {
    private String email;
    private String password;
    private AuthProvider provider;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public AuthProvider getProvider() {
        return provider;
    }

    public void setProvider(AuthProvider provider) {
        this.provider = provider;
    }
    
    public Account toDomain() {
        return AccountBuilder.anAccount()
            .withEmail(Email.of(email))
            .withProvider(provider)
            .withPassword(Password.of(password))
            .build();
    }
}
