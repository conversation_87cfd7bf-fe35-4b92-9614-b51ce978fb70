package org.helply.account.application.command;

import org.helply.account.domain.AuthProvider;

public class ThirdPartyLoginCommand {
    private String token;
    private AuthProvider provider;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public AuthProvider getProvider() {
        return provider;
    }

    public void setProvider(AuthProvider provider) {
        this.provider = provider;
    }
}
