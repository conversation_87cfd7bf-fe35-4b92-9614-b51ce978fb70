package org.helply.account.application.query;

import org.helply.account.domain.Account;
import org.helply.account.domain.AuthProvider;

import java.time.LocalDateTime;

public class AccountDto {
    private String email;
    private LocalDateTime lastLoginTime;
    private LocalDateTime lastModifyTime;
    private AuthProvider provider;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public AuthProvider getProvider() {
        return provider;
    }

    public void setProvider(AuthProvider provider) {
        this.provider = provider;
    }

    public static AccountDto fromDomain(Account account) {
        AccountDto dto = new AccountDto();
        dto.setEmail(account.getEmail().getValue());
        dto.setProvider(account.getProvider());
        dto.setLastLoginTime(account.getLastLoginTime());
        dto.setLastModifyTime(account.getLastModifyTime());
        return dto;
    }
}
