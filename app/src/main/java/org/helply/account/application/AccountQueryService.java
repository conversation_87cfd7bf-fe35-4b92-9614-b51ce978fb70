package org.helply.account.application;

import org.helply.account.application.query.AccountDto;
import org.helply.account.domain.Account;
import org.helply.account.domain.AccountRepository;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.shared.domain.Email;
import org.springframework.stereotype.Service;

@Service
public class AccountQueryService {
    private final AccountRepository accountRepository;

    public AccountQueryService(AccountRepository accountRepository) {
        this.accountRepository = accountRepository;
    }

    public AccountDto getAccountByEmail(String rawEmail) {
        Email email = Email.of(rawEmail);
        Account account = accountRepository.findByEmail(email)
            .orElseThrow(() -> new AccountNotFoundException(email));
        
        return AccountDto.fromDomain(account);
    }
}
