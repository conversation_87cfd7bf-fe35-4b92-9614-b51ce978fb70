package org.helply.account.application;

import org.helply.account.application.command.LocalLoginCommand;
import org.helply.account.application.command.RegisterAccountCommand;
import org.helply.account.application.command.ThirdPartyLoginCommand;
import org.helply.account.application.query.AccessTokenDto;
import org.helply.account.domain.*;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.shared.domain.Email;
import org.helply.account.domain.Authenticator;
import org.helply.account.domain.PasswordHasher;
import org.helply.account.infrastructure.security.GoogleTokenVerifier;
import org.helply.user.domain.User;
import org.helply.user.domain.UserBuilder;
import org.helply.user.domain.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class AuthApplicationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthApplicationService.class);

    private final AccountRepository accountRepository;
    private final PasswordHasher passwordHasher;
    private final Authenticator authenticator;
    private final GoogleTokenVerifier googleTokenVerifier;
    private final UserRepository userRepository;

    public AuthApplicationService(AccountRepository accountRepository, PasswordHasher passwordHasher, Authenticator authenticator, GoogleTokenVerifier googleTokenVerifier, UserRepository userRepository) {
        this.accountRepository = accountRepository;
        this.passwordHasher = passwordHasher;
        this.authenticator = authenticator;
        this.googleTokenVerifier = googleTokenVerifier;
        this.userRepository = userRepository;
    }

    public void register(RegisterAccountCommand command) {
        UserBuilder userBuilder = UserBuilder.anUser().withEmail(Email.of(command.getEmail()));
        User user = userRepository.save(userBuilder.build());

        Account account = command.toDomain();
        account.linkToUser(user.getId());
        
        PlainPassword plainPassword = PlainPassword.of(command.getPassword());
        account.changePassword(plainPassword, passwordHasher);
        
        accountRepository.save(account);
    }

    public AccessTokenDto login(LocalLoginCommand command) {
        Email email = Email.of(command.getEmail());
        PlainPassword plainPassword = PlainPassword.of(command.getPassword());
        Account account = accountRepository.findByEmail(email).orElseThrow(() -> new AccountNotFoundException(email));
        account.checkPassword(plainPassword, passwordHasher);

        String loginToken = authenticator.token(account.getEmail());
        postLogin(account);
        
        AccessTokenDto result = new AccessTokenDto();
        result.setToken(loginToken);
        
        return result;
    }

    public AccessTokenDto sso(ThirdPartyLoginCommand command) {
        final IdToken idToken = getIdToken(command.getToken(), command.getProvider());

        Objects.requireNonNull(idToken);

        Account account = accountRepository.findByEmail(idToken.getEmail())
            .orElseGet(() -> {
                Account newAccount = AccountBuilder.anAccount()
                    .withProvider(idToken.getAuthProvider())
                    .withEmail(idToken.getEmail())
                    .build();

                return accountRepository.save(newAccount);
            });

        String loginToken = authenticator.token(idToken.getEmail());
        postLogin(account);
        
        AccessTokenDto result = new AccessTokenDto();
        result.setToken(loginToken);

        return result;
    }

    private void postLogin(Account account) {
        account.login();
        accountRepository.save(account);
    }

    private IdToken getIdToken(String token, AuthProvider authProvider) {
        if (authProvider == AuthProvider.GOOGLE) {
            return googleTokenVerifier.extractToken(token);
        } else {
            LOGGER.warn("SSO doesn't support provider: {}", authProvider);
            return null;
        }
    }
}
