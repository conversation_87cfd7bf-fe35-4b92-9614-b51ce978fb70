package org.helply.account.infrastructure.security;

import org.apache.commons.lang3.StringUtils;
import org.helply.account.domain.Password;
import org.helply.account.domain.PasswordHasher;
import org.helply.account.domain.PlainPassword;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;
import java.util.Objects;

@Component
public class Pbkdf2PasswordHasher implements PasswordHasher {

    // Configuration
    private static final int SALT_LENGTH = 16;          // in bytes
    private static final int ITERATIONS = 65536;
    private static final int KEY_LENGTH = 256;          // in bits
    private static final String ALGORITHM = "PBKDF2WithHmacSHA256";

    @Override
    public Password hash(PlainPassword plainPassword) {
        Objects.requireNonNull(plainPassword);
        byte[] salt = generateSalt();
        String hashString = hashInternal(plainPassword.getValue(), salt);
        String saltString = Base64.getEncoder().encodeToString(salt);
        return new Password(saltString + "$" + hashString);
    }

    private String hashInternal(String password, byte[] salt) {
        PBEKeySpec spec = new PBEKeySpec(password.toCharArray(), salt, ITERATIONS, KEY_LENGTH);
        try {
            SecretKeyFactory skf = SecretKeyFactory.getInstance(ALGORITHM);
            byte[] hash = skf.generateSecret(spec).getEncoded();
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new RuntimeException("Failed to hash value", e);
        }
    }

    private byte[] generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return salt;
    }

    @Override
    public boolean matches(PlainPassword plainPassword, Password password) {
        String[] split = password.getValue().split("\\$");
        if (split.length != 2) {
            throw new RuntimeException("");
        }

        byte[] salt = Base64.getDecoder().decode(split[0]);
        String targetHash = split[1];
        String hash = hashInternal(plainPassword.getValue(), salt);

        return StringUtils.equals(targetHash, hash);
    }
}
