package org.helply.account.infrastructure.persistence.mapper;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountBuilder;
import org.helply.account.domain.AccountId;
import org.helply.account.domain.Password;
import org.helply.account.infrastructure.persistence.jpa.AccountJpaEntity;
import org.helply.shared.domain.Email;
import org.helply.user.domain.UserId;
import org.springframework.stereotype.Component;

@Component
public class AccountEntityMapper {
    
    public AccountJpaEntity toEntity(Account account) {
        AccountJpaEntity entity = new AccountJpaEntity();
        if (account.getId() != null) {
            entity.setId(account.getId().getValue());
        }
        entity.setEmail(account.getEmail().getValue());
        entity.setPassword(account.getPassword().getValue());
        entity.setProvider(account.getProvider());
        entity.setLastLoginTime(account.getLastLoginTime());
        entity.setUserId(account.getUserId().getValue());
        return entity;
    }
    
    public Account toDomain(AccountJpaEntity entity) {
        Account account = AccountBuilder.anAccount()
            .withId(AccountId.of(entity.getId()))
            .withEmail(Email.of(entity.getEmail()))
            .withPassword(Password.of(entity.getPassword()))
            .withProvider(entity.getProvider())
            .withLastLoginTime(entity.getLastLoginTime())
            .withUserId(UserId.of(entity.getUserId()))
            .build();
        
        return account;
    }
}
