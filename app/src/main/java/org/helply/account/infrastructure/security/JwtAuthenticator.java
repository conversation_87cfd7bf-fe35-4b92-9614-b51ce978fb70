package org.helply.account.infrastructure.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import org.helply.account.domain.Authenticator;
import org.helply.shared.domain.Email;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;

@Component
public class JwtAuthenticator implements Authenticator {
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtAuthenticator.class);
    private static final String ISSUER = "helply-jwt";

    private final KeyPair keyPair;
    private final long expiration;

    public JwtAuthenticator(Environment environment) {
        expiration = Objects.requireNonNull(environment.getProperty(EXPIRATION, Long.class));
        String privateKeyValue = environment.getProperty(PRIVATE_KEY_VALUE);
        String publicKeyValue = environment.getProperty(PUBLIC_KEY_VALUE);
        byte[] privateKeyBytes = Decoders.BASE64.decode(privateKeyValue);
        byte[] publicKeyBytes = Decoders.BASE64.decode(publicKeyValue);

        try {
            KeyFactory keyFactory = KeyFactory.getInstance("EdDSA");

            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(publicKeyBytes));
            PrivateKey privateKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(privateKeyBytes));
            keyPair = new KeyPair(publicKey, privateKey);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String token(Email email) {
        Instant now = Instant.now();
        Instant expirationInstant = now.plus(expiration, ChronoUnit.MILLIS);

        return Jwts.builder()
            .issuer(ISSUER)
            .subject(email.getValue())
            .issuedAt(Date.from(now))
            .expiration(Date.from(expirationInstant))
            .signWith(keyPair.getPrivate())
            .compact();
    }

    @Override
    public boolean verify(String token) {
        try {
            Jws<Claims> claimsJws = Jwts.parser().verifyWith(keyPair.getPublic()).build().parseSignedClaims(token);
            //OK, we can trust this JWT
            Date expiration = claimsJws.getPayload().getExpiration();
            String issuer = claimsJws.getPayload().getIssuer();
            return ISSUER.equals(issuer) && (new Date()).before(expiration);
        } catch (SecurityException ex) {
            LOGGER.error("Invalid JWT signature: {}", token);
        } catch (MalformedJwtException ex) {
            LOGGER.error("Invalid JWT token: {}", token);
        } catch (ExpiredJwtException ex) {
            LOGGER.error("Expired JWT token: {}", token);
        } catch (UnsupportedJwtException ex) {
            LOGGER.error("Unsupported JWT token: {}", token);
        } catch (IllegalArgumentException ex) {
            LOGGER.error("JWT claims string is empty: {}", token);
        } catch (JwtException ex) {
            LOGGER.error("Cannot verify the JWT: {}", token);
        }
        return false;
    }

    @Override
    public Email getEmailFromToken(String token) {
        Jws<Claims> claimsJws = Jwts.parser().verifyWith(keyPair.getPublic()).build().parseSignedClaims(token);
        String email = claimsJws.getPayload().getSubject();
        return new Email(email);
    }
}
