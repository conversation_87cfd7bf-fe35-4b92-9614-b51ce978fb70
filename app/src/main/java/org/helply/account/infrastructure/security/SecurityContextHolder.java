package org.helply.account.infrastructure.security;

import java.util.Objects;

public class SecurityContextHolder {
    private static final ThreadLocal<SecurityContext> contextHolder = new ThreadLocal<>();

    public static SecurityContext getContext() {
        SecurityContext securityContext = contextHolder.get();
        if (securityContext == null) {
            securityContext = SecurityContext.anonymous();
            setContext(securityContext);
        }
        return securityContext;
    }

    public static void setContext(SecurityContext securityContext) {
        Objects.requireNonNull(securityContext);

        contextHolder.set(securityContext);
    }


}
