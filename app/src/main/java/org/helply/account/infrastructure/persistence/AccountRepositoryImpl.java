package org.helply.account.infrastructure.persistence;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountId;
import org.helply.account.domain.AccountRepository;
import org.helply.account.infrastructure.persistence.jpa.AccountJpaEntity;
import org.helply.account.infrastructure.persistence.jpa.AccountJpaRepository;
import org.helply.account.infrastructure.persistence.mapper.AccountEntityMapper;
import org.helply.shared.domain.Email;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class AccountRepositoryImpl implements AccountRepository {
    private final AccountJpaRepository accountJpaRepository;
    private final AccountEntityMapper accountEntityMapper;

    public AccountRepositoryImpl(AccountJpaRepository accountJpaRepository, AccountEntityMapper accountEntityMapper) {
        this.accountJpaRepository = accountJpaRepository;
        this.accountEntityMapper = accountEntityMapper;
    }

    @Override
    public Account save(Account account) {
        AccountJpaEntity saved = accountJpaRepository.save(accountEntityMapper.toEntity(account));
        return accountEntityMapper.toDomain(saved);
    }

    @Override
    public void delete(AccountId accountId) {
        accountJpaRepository.deleteById(accountId.getValue());
    }

    @Override
    public Optional<Account> findById(AccountId accountId) {
        return accountJpaRepository.findById(accountId.getValue()).map(accountEntityMapper::toDomain);
    }

    @Override
    public Optional<Account> findByEmail(Email email) {
        return accountJpaRepository.findByEmail(email.getValue()).map(accountEntityMapper::toDomain);
    }
}
