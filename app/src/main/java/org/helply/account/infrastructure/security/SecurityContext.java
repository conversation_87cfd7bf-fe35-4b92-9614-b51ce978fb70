package org.helply.account.infrastructure.security;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountBuilder;
import org.helply.account.domain.AuthProvider;
import org.helply.shared.domain.Email;

public class SecurityContext {
    private final Account account;

    public SecurityContext(Account account) {
        this.account = account;
    }

    public Account getAccount() {
        return account;
    }

    public static SecurityContext anonymous() {
        Account account = AccountBuilder.anAccount()
            .withEmail(new Email("<EMAIL>"))
            .withProvider(AuthProvider.LOCAL)
            .build();

        return new SecurityContext(account);
    }
}
