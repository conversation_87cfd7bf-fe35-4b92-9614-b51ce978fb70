package org.helply.account.infrastructure.security;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import org.helply.account.domain.IdToken;
import org.helply.account.domain.AuthProvider;
import org.helply.shared.domain.Email;
import org.helply.user.domain.Name;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

@Component
public class GoogleTokenVerifier {
    private static final String CLIENT_ID = "YOUR_CLIENT_ID.apps.googleusercontent.com";
    private static final NetHttpTransport transport = new NetHttpTransport();
    private static final JsonFactory jsonFactory = GsonFactory.getDefaultInstance();

    public IdToken extractToken(String idTokenString) {
        try {
            GoogleIdTokenVerifier verifier = new GoogleIdTokenVerifier.Builder(transport, jsonFactory)
                .setAudience(Collections.singletonList(CLIENT_ID))
                .build();

            GoogleIdToken idToken = verifier.verify(idTokenString);
            Objects.requireNonNull(idToken);

            return new IdToken(AuthProvider.GOOGLE,
                new Name((String) idToken.getPayload().get("name")),
                new Email(idToken.getPayload().getEmail()));
        } catch (Exception e) {
            throw new RuntimeException("Invalid ID token.");
        }
    }

}
