package org.helply.account.infrastructure.security;

import org.helply.account.domain.Account;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Collections;

/**
 * Custom authentication token for JWT-based authentication
 */
public class JwtAuthenticationToken extends AbstractAuthenticationToken {

    private final String token;
    private final Account account;

    /**
     * Constructor for unauthenticated token (before authentication)
     */
    public JwtAuthenticationToken(String token) {
        super(Collections.emptyList());
        this.token = token;
        this.account = null;
        setAuthenticated(false);
    }

    /**
     * Constructor for authenticated token (after successful authentication)
     */
    public JwtAuthenticationToken(String token, Account account, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.token = token;
        this.account = account;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return token;
    }

    @Override
    public Object getPrincipal() {
        return account;
    }

    public String getToken() {
        return token;
    }

    public Account getAccount() {
        return account;
    }
}
