package org.helply.account.domain;

import org.helply.account.exception.PasswordNotMatchedException;
import org.helply.shared.domain.AggregateRoot;
import org.helply.shared.domain.Email;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public class Account extends AggregateRoot<AccountId> {

    private final AccountId id;
    private Email email;
    private Password password;
    private LocalDateTime lastLoginTime;
    private LocalDateTime lastModifyTime;
    private AuthProvider provider;
    private UserId userId;

    public Account(AccountId id, Email email, Password password, AuthProvider provider, LocalDateTime lastLoginTime, LocalDateTime lastModifyTime, UserId userId) {
        this.id = id;
        this.email = email;
        this.password = password;
        this.provider = provider;
        this.lastLoginTime = lastLoginTime;
        this.lastModifyTime = lastModifyTime;
        this.userId = userId;
    }
    
    public void changePassword(PlainPassword plainPassword, PasswordHasher passwordHasher) {
        this.password = passwordHasher.hash(plainPassword);
    }
    
    public void checkPassword(PlainPassword plainPassword, PasswordHasher passwordHasher) throws PasswordNotMatchedException {
        if (!passwordHasher.matches(plainPassword, password)) {
            throw new PasswordNotMatchedException();
        }
    }
    
    public void linkToUser(UserId userId) {
        this.userId = userId;
    }
    
    public void login() {
        this.lastLoginTime = LocalDateTime.now();
    }

    @Override
    public AccountId getId() {
        return id;
    }

    public Email getEmail() {
        return email;
    }

    public Password getPassword() {
        return password;
    }

    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public AuthProvider getProvider() {
        return provider;
    }

    public UserId getUserId() {
        return userId;
    }
}
