package org.helply.account.domain;

import org.apache.commons.lang3.StringUtils;
import org.helply.shared.domain.ValueObject;

public class PlainPassword extends ValueObject {
    private final String value;

    public PlainPassword(String value) {
        this.value = value;
        validate();
    }

    public String getValue() {
        return value;
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] {value};
    }

    @Override
    protected void validate() {
        if (StringUtils.isBlank(value)) {
            throw new IllegalArgumentException("value cannot be empty!");
        }
        if (value.length() < 10) {
            throw new IllegalArgumentException("value must have at least 10 characters!");
        }

        boolean hasLowerCase = false;
        boolean hasUpperCase = false;
        boolean hasOtherSymbol = false;
        boolean hasNumber = false;
        for (char c : value.toCharArray()) {
            if (c >= 'a' && c <= 'z') {
                hasLowerCase = true;
            } else if (c >= 'A' && c <= 'Z') {
                hasUpperCase = true;
            } else if (c >= '0' && c <= '9') {
                hasNumber = true;
            } else {
                hasOtherSymbol = true;
            }
        }

        if (!(hasLowerCase && hasUpperCase && hasOtherSymbol & hasNumber)) {
            throw new IllegalArgumentException("value must have at least one lower case character, one upper case character, one number and one other symbol!");
        }
    }
    
    public static PlainPassword of(String value) {
        return new PlainPassword(value);
    }
}
