package org.helply.account.domain;

import org.apache.commons.lang3.StringUtils;
import org.helply.shared.domain.ValueObject;

public class Password extends ValueObject {
    private final String value;
    
    public Password(String value) {
        this.value = value;
        validate();
    }

    public String getValue() {
        return value;
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] {value};
    }
    
    @Override
    protected void validate() {
        if (StringUtils.isBlank(value)) {
            throw new IllegalArgumentException("value cannot be empty!");
        }
    }

    public static Password of(String value) {
        return new Password(value);
    }
}
