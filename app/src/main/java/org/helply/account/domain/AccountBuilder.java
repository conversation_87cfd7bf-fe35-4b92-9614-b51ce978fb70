package org.helply.account.domain;

import org.helply.shared.domain.Email;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public final class AccountBuilder {

    private AccountId id;
    private Email email;
    private Password password;
    private LocalDateTime lastLoginTime;
    private LocalDateTime lastModifyTime;
    private AuthProvider provider;
    private UserId userId;

    private AccountBuilder() {
    }

    public static AccountBuilder anAccount() {
        return new AccountBuilder();
    }

    public AccountBuilder withId(AccountId id) {
        this.id = id;
        return this;
    }

    public AccountBuilder withEmail(Email email) {
        this.email = email;
        return this;
    }

    public AccountBuilder withPassword(Password password) {
        this.password = password;
        return this;
    }

    public AccountBuilder withLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
        return this;
    }

    public AccountBuilder withLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
        return this;
    }

    public AccountBuilder withProvider(AuthProvider provider) {
        this.provider = provider;
        return this;
    }
    
    public AccountBuilder withUserId(UserId userId) {
        this.userId = userId;
        return this;
    }

    public Account build() {
        return new Account(id, email, password, provider, lastLoginTime, lastModifyTime, userId);
    }
}
