package org.helply.account.domain;

import org.helply.shared.domain.Email;
import org.helply.shared.domain.ValueObject;
import org.helply.user.domain.Name;

public class IdToken extends ValueObject {
    private Email email;
    private Name name;
    private AuthProvider authProvider;

    public IdToken(AuthProvider authProvider, Name name, Email email) {
        this.authProvider = authProvider;
        this.name = name;
        this.email = email;
        validate();
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] {email, authProvider};
    }

    @Override
    protected void validate() {
        super.validate();
    }

    public Email getEmail() {
        return email;
    }

    public Name getName() {
        return name;
    }

    public AuthProvider getAuthProvider() {
        return authProvider;
    }
}
