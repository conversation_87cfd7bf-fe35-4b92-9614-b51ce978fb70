package org.helply.account.exception;

import org.helply.account.domain.AccountId;
import org.helply.shared.domain.Email;

public class AccountNotFoundException extends RuntimeException {
    public AccountNotFoundException(AccountId accountId) {
        super("Account not found with id: " + accountId.getValue());
    }

    public AccountNotFoundException(Email email) {
        super("Account not found with email: " + email.getValue());
    }
}
