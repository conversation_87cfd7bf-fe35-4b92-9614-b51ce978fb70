package org.helply.task.domain;

import org.helply.shared.domain.Money;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public final class TaskBuilder {

    private TaskId id;
    private TaskTitle title;
    private TaskDescription description;
    private TaskCategory category;
    private Money price;
    private LocalDateTime lastModifyTime;
    private UserId providerId;

    private TaskBuilder() {
    }

    public static TaskBuilder aTask() {
        return new TaskBuilder();
    }

    public TaskBuilder withId(TaskId id) {
        this.id = id;
        return this;
    }

    public TaskBuilder withTitle(TaskTitle title) {
        this.title = title;
        return this;
    }

    public TaskBuilder withDescription(TaskDescription description) {
        this.description = description;
        return this;
    }

    public TaskBuilder withCategory(TaskCategory category) {
        this.category = category;
        return this;
    }

    public TaskBuilder withPrice(Money price) {
        this.price = price;
        return this;
    }

    public TaskBuilder withLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
        return this;
    }

    public TaskBuilder withProviderId(UserId providerId) {
        this.providerId = providerId;
        return this;
    }

    public Task build() {
        return new Task(id, title, description, category, price, lastModifyTime, providerId);
    }
}
