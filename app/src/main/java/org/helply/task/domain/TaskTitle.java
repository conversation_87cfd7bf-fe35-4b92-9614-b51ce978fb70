package org.helply.task.domain;

import org.helply.shared.domain.ValueObject;

import java.util.Objects;

public class TaskTitle extends ValueObject {

    private final String value;

    public TaskTitle(String value) {
        this.value = value;
        validate();
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] {value};
    }

    public static TaskTitle of(String value) {
        return new TaskTitle(value);
    }

    @Override
    protected void validate() {
        Objects.requireNonNull(value);
    }

    public String getValue() {
        return value;
    }
}
