package org.helply.task.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface TaskRepository {

    Task save(Task task);

    void delete(TaskId taskId);

    Optional<Task> findById(TaskId taskId);

    List<Task> findAll();

    List<Task> findByCategory(TaskCategory category);

    List<Task> findByTitleOrDescriptionContaining(String keyword);

    List<Task> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);

    List<Task> findByCategoryAndPriceLessThanEqual(TaskCategory category, BigDecimal maxPrice);
}
