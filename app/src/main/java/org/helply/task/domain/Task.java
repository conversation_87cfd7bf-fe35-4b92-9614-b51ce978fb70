package org.helply.task.domain;

import org.helply.shared.domain.AggregateRoot;
import org.helply.shared.domain.Money;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public class Task extends AggregateRoot<TaskId> {
    private TaskId id;
    private TaskTitle title;
    private TaskDescription description;
    private TaskCategory category;
    private Money price;
    private LocalDateTime lastModifyTime;
    private UserId providerId;

    public Task(TaskId id, TaskTitle title, TaskDescription description, TaskCategory category, Money price, LocalDateTime lastModifyTime, UserId providerId) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.category = category;
        this.price = price;
        this.lastModifyTime = lastModifyTime;
        this.providerId = providerId;
    }

    @Override
    public TaskId getId() {
        return id;
    }

    public TaskTitle getTitle() {
        return title;
    }

    public TaskDescription getDescription() {
        return description;
    }

    public TaskCategory getCategory() {
        return category;
    }

    public Money getPrice() {
        return price;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public UserId getProviderId() {
        return providerId;
    }
}
