package org.helply.task.domain;

import org.helply.shared.domain.ValueObject;

import java.util.Objects;

public class TaskDescription extends ValueObject {
    
    private final String value;
    
    public TaskDescription(String value) {
        this.value = value;
        validate();
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] {value};
    }
    
    public static TaskDescription of(String value) {
        return new TaskDescription(value);
    }

    @Override
    protected void validate() {
        Objects.requireNonNull(value);
    }

    public String getValue() {
        return value;
    }
}
