package org.helply.task.infrastructure.persistence.jpa;

import org.helply.task.domain.TaskCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TaskJpaRepository extends JpaRepository<TaskJpaEntity, Long> {

    List<TaskJpaEntity> findByCategory(TaskCategory category);

    @Query("SELECT t FROM TaskJpaEntity t WHERE t.title LIKE %:keyword% OR t.description LIKE %:keyword%")
    List<TaskJpaEntity> findByTitleOrDescriptionContaining(@Param("keyword") String keyword);

    @Query("SELECT t FROM TaskJpaEntity t WHERE t.price BETWEEN :minPrice AND :maxPrice")
    List<TaskJpaEntity> findByPriceBetween(@Param("minPrice") java.math.BigDecimal minPrice,
                                           @Param("maxPrice") java.math.BigDecimal maxPrice);

    @Query("SELECT t FROM TaskJpaEntity t WHERE t.category = :category AND t.price <= :maxPrice")
    List<TaskJpaEntity> findByCategoryAndPriceLessThanEqual(@Param("category") TaskCategory category,
                                                            @Param("maxPrice") java.math.BigDecimal maxPrice);
}
