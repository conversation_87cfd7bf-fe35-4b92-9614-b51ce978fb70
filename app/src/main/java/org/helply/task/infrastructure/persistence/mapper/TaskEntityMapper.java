package org.helply.task.infrastructure.persistence.mapper;

import org.helply.shared.domain.Money;
import org.helply.task.domain.TaskBuilder;
import org.helply.task.domain.Task;
import org.helply.task.domain.TaskDescription;
import org.helply.task.domain.TaskId;
import org.helply.task.domain.TaskTitle;
import org.helply.user.domain.UserId;
import org.helply.task.infrastructure.persistence.jpa.TaskJpaEntity;
import org.springframework.stereotype.Component;

@Component
public class TaskEntityMapper {

    public Task toDomain(TaskJpaEntity entity) {
        return TaskBuilder.aTask()
            .withId(entity.getId() != null ? new TaskId(entity.getId()) : null)
            .withTitle(entity.getTitle() != null ? new TaskTitle(entity.getTitle()) : null)
            .withDescription(entity.getDescription() != null ? new TaskDescription(entity.getDescription()) : null)
            .withCategory(entity.getCategory())
            .withPrice(entity.getPrice() != null ? Money.of(entity.getPrice()) : null)
            .withLastModifyTime(entity.getLastModifyTime())
            .withProviderId(UserId.of(entity.getUserId()))
            .build();
    }

    public TaskJpaEntity toEntity(Task task) {
        TaskJpaEntity entity = new TaskJpaEntity();

        if (task.getId() != null) {
            entity.setId(task.getId().getValue());
        }

        if (task.getTitle() != null) {
            entity.setTitle(task.getTitle().getValue());
        }

        if (task.getDescription() != null) {
            entity.setDescription(task.getDescription().getValue());
        }

        entity.setCategory(task.getCategory());

        if (task.getPrice() != null) {
            entity.setPrice(task.getPrice().getAmount());
        }

        entity.setUserId(task.getProviderId().getValue());

        entity.setLastModifyTime(task.getLastModifyTime());

        return entity;
    }
}
