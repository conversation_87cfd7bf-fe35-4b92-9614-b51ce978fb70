package org.helply.task.infrastructure.persistence;

import org.helply.task.domain.Task;
import org.helply.task.domain.TaskRepository;
import org.helply.task.domain.TaskCategory;
import org.helply.task.domain.TaskId;
import org.helply.task.infrastructure.persistence.jpa.TaskJpaEntity;
import org.helply.task.infrastructure.persistence.jpa.TaskJpaRepository;
import org.helply.task.infrastructure.persistence.mapper.TaskEntityMapper;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Component
public class TaskRepositoryImpl implements TaskRepository {

    private final TaskEntityMapper taskEntityMapper;
    private final TaskJpaRepository taskJpaRepository;

    public TaskRepositoryImpl(TaskEntityMapper taskEntityMapper, TaskJpaRepository taskJpaRepository) {
        this.taskEntityMapper = taskEntityMapper;
        this.taskJpaRepository = taskJpaRepository;
    }

    @Override
    public Task save(Task task) {
        TaskJpaEntity taskEntity = taskEntityMapper.toEntity(task);
        TaskJpaEntity saved = taskJpaRepository.save(taskEntity);
        return taskEntityMapper.toDomain(saved);
    }

    @Override
    public void delete(TaskId taskId) {
        taskJpaRepository.deleteById(taskId.getValue());
    }

    @Override
    public Optional<Task> findById(TaskId taskId) {
        Optional<TaskJpaEntity> taskEntity = taskJpaRepository.findById(taskId.getValue());
        return taskEntity.map(taskEntityMapper::toDomain);
    }

    @Override
    public List<Task> findAll() {
        List<TaskJpaEntity> entities = taskJpaRepository.findAll();
        return entities.stream()
            .map(taskEntityMapper::toDomain)
            .toList();
    }

    @Override
    public List<Task> findByCategory(TaskCategory category) {
        List<TaskJpaEntity> entities = taskJpaRepository.findByCategory(category);
        return entities.stream()
            .map(taskEntityMapper::toDomain)
            .toList();
    }

    @Override
    public List<Task> findByTitleOrDescriptionContaining(String keyword) {
        List<TaskJpaEntity> entities = taskJpaRepository.findByTitleOrDescriptionContaining(keyword);
        return entities.stream()
            .map(taskEntityMapper::toDomain)
            .toList();
    }

    @Override
    public List<Task> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice) {
        List<TaskJpaEntity> entities = taskJpaRepository.findByPriceBetween(minPrice, maxPrice);
        return entities.stream()
            .map(taskEntityMapper::toDomain)
            .toList();
    }

    @Override
    public List<Task> findByCategoryAndPriceLessThanEqual(TaskCategory category, BigDecimal maxPrice) {
        List<TaskJpaEntity> entities = taskJpaRepository.findByCategoryAndPriceLessThanEqual(category, maxPrice);
        return entities.stream()
            .map(taskEntityMapper::toDomain)
            .toList();
    }
}
