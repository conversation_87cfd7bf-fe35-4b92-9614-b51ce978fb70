package org.helply.task.application;

import org.helply.task.application.query.TaskDto;
import org.helply.task.domain.Task;
import org.helply.task.domain.TaskCategory;
import org.helply.task.domain.TaskId;
import org.helply.task.domain.TaskRepository;
import org.helply.task.exception.TaskNotFoundException;
import org.helply.user.domain.User;
import org.helply.user.domain.UserId;
import org.helply.user.domain.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TaskQueryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskQueryService.class);
 
    private final TaskRepository taskRepository;
    private final UserRepository userRepository;

    public TaskQueryService(TaskRepository taskRepository, UserRepository userRepository) {
        this.taskRepository = taskRepository;
        this.userRepository = userRepository;
    }
    
    public List<TaskDto> getAllTasks() {
        LOGGER.info("Getting all tasks");
        List<Task> tasks = taskRepository.findAll();
        return getTaskDtos(tasks);
    }

    public TaskDto getTaskById(Long id) {
        TaskId taskId = TaskId.of(id);
        LOGGER.info("Getting task by id: {}", taskId.getValue());
        Task task = taskRepository.findById(taskId)
            .orElseThrow(() -> new TaskNotFoundException(taskId));

        List<TaskDto> taskDtos = getTaskDtos(Collections.singletonList(task));
        return taskDtos.get(0);
    }

    public List<TaskDto> getTasksByCategory(TaskCategory category) {
        LOGGER.info("Getting tasks by category: {}", category);
        List<Task> tasks = taskRepository.findByCategory(category);
        return getTaskDtos(tasks);
    }

    public List<TaskDto> searchTasks(String keyword) {
        LOGGER.info("Searching tasks with keyword: {}", keyword);
        List<Task> tasks = taskRepository.findByTitleOrDescriptionContaining(keyword);
        return getTaskDtos(tasks);
    }

    public List<TaskDto> getTasksByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        LOGGER.info("Getting tasks by price range: {} - {}", minPrice, maxPrice);
        List<Task> tasks = taskRepository.findByPriceBetween(minPrice, maxPrice);
        return getTaskDtos(tasks);
    }

    public List<TaskDto> getTasksByCategoryAndMaxPrice(TaskCategory category, BigDecimal maxPrice) {
        LOGGER.info("Getting tasks by category: {} and max price: {}", category, maxPrice);
        List<Task> tasks = taskRepository.findByCategoryAndPriceLessThanEqual(category, maxPrice);
        return getTaskDtos(tasks);
    }

    private List<TaskDto> getTaskDtos(List<Task> tasks) {
        Set<UserId> userIds = tasks.stream().map(Task::getProviderId).collect(Collectors.toSet());
        Map<UserId, User> users = userRepository.findByIds(userIds).stream().collect(Collectors.toMap(User::getId, Function.identity()));

        List<TaskDto> result = new ArrayList<>(tasks.size());

        for (Task task : tasks) {
            TaskDto taskDto = TaskDto.fromDomain(task, users.get(task.getProviderId()));
            result.add(taskDto);
        }
        return result;
    }
}
