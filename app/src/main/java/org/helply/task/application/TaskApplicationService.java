package org.helply.task.application;

import org.helply.task.application.command.DeleteTaskCommand;
import org.helply.task.application.command.SaveTaskCommand;
import org.helply.task.exception.TaskNotFoundException;
import org.helply.task.domain.Task;
import org.helply.task.domain.TaskRepository;
import org.helply.task.domain.TaskId;
import org.helply.user.application.UserQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class TaskApplicationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskApplicationService.class);

    private final TaskRepository taskRepository;
    
    private final UserQueryService userQueryService;

    public TaskApplicationService(TaskRepository taskRepository, UserQueryService userQueryService) {
        this.taskRepository = taskRepository;
        this.userQueryService = userQueryService;
    }

    public Long createTask(SaveTaskCommand command) {
        userQueryService.findUserById(command.getProviderId());

        Task task = command.toDomain();
        LOGGER.info("Saving task with id: {}", task.getId() != null ? task.getId().getValue() : "new");
        Task savedTask = taskRepository.save(task);
        
        return savedTask.getId().getValue();
    }
    
    public void modifyTask(SaveTaskCommand command) {

        Task task = command.toDomain();
        taskRepository.findById(task.getId())
            .orElseThrow(() -> new TaskNotFoundException(task.getId()));

        taskRepository.save(task);
    }
    
    public void deleteTask(DeleteTaskCommand command) {
        TaskId taskId = TaskId.of(command.getId());
        LOGGER.info("Deleting task with id: {}", taskId.getValue());
        // Verify task exists before deletion
        taskRepository.findById(taskId)
            .orElseThrow(() -> new TaskNotFoundException(taskId));
        taskRepository.delete(taskId);
    }

}
