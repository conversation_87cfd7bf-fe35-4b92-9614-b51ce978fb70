package org.helply.task.application.command;

import org.helply.shared.domain.Money;
import org.helply.task.domain.*;
import org.helply.user.domain.UserId;

import java.math.BigDecimal;

public class SaveTaskCommand {
    private Long id;

    private String title;

    private String description;

    private TaskCategory category;

    private BigDecimal price;

    private Long providerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TaskCategory getCategory() {
        return category;
    }

    public void setCategory(TaskCategory category) {
        this.category = category;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public Task toDomain() {
        return TaskBuilder.aTask()
            .withId(TaskId.of(id))
            .withTitle(TaskTitle.of(title))
            .withDescription(TaskDescription.of(description))
            .withCategory(category)
            .withPrice(Money.of(price))
            .withProviderId(UserId.of(providerId))
            .build();
    }
}
