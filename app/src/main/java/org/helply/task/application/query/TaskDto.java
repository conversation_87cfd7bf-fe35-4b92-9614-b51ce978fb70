package org.helply.task.application.query;

import org.helply.task.domain.Task;
import org.helply.task.domain.TaskCategory;
import org.helply.task.domain.TaskId;
import org.helply.user.domain.User;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TaskDto {
    private Long id;
    private String title;
    private String description;
    private TaskCategory category;
    private BigDecimal price;
    private LocalDateTime lastModifyTime;
    private Long providerId;
    private String providerName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TaskCategory getCategory() {
        return category;
    }

    public void setCategory(TaskCategory category) {
        this.category = category;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }
    
    public static TaskDto fromDomain(Task task, User user) {
        TaskDto dto = new TaskDto();
        dto.setId(task.getId().getValue());
        dto.setCategory(task.getCategory());
        dto.setTitle(task.getTitle().getValue());
        dto.setDescription(task.getDescription().getValue());
        dto.setPrice(task.getPrice().getAmount());
        dto.setLastModifyTime(task.getLastModifyTime());
        dto.setProviderId(task.getProviderId().getValue());
        dto.setProviderName(user.getName().getValue());
        return dto;
    }
 }
