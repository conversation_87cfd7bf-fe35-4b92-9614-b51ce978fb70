package org.helply.web;

import org.helply.chat.application.ChatService;
import org.helply.chat.application.command.CreateMessageCommand;
import org.helply.web.request.chat.ChatMessageRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;

@Controller
public class ChatController {

    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);

    private final ChatService chatService;

    public ChatController(ChatService chatService) {
        this.chatService = chatService;
    }

    /**
     * Handle incoming chat messages from WebSocket clients
     * Endpoint: /app/chat.sendMessage
     */
    @MessageMapping("/chat.sendMessage")
    public void sendMessage(@Payload ChatMessageRequest request) {
        logger.info("Received chat message: {}", request.getContent());

        try {
            // Convert request to command
            CreateMessageCommand command = new CreateMessageCommand();
            command.setConversationId(Long.parseLong(request.getConversationId()));
            command.setSenderId(Long.parseLong(request.getSenderId()));
            command.setContent(request.getContent());
            command.setType(request.getType());

            // Process the message
            chatService.createMessage(command);

        } catch (Exception e) {
            logger.error("Error processing chat message: {}", e.getMessage(), e);
        }
    }
}