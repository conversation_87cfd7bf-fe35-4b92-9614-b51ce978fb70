package org.helply.web;

import org.helply.chat.application.ChatService;
import org.helply.chat.application.command.CreateMessageCommand;
import org.helply.user.application.UserQueryService;
import org.helply.user.application.query.UserDto;
import org.helply.web.request.chat.ChatMessageRequest;
import org.helply.account.application.AuthenticationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;

@Controller
public class ChatController {

    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);

    private final ChatService chatService;
    private final AuthenticationService authenticationService;
    private final UserQueryService userQueryService;

    public ChatController(ChatService chatService, AuthenticationService authenticationService,
                         UserQueryService userQueryService) {
        this.chatService = chatService;
        this.authenticationService = authenticationService;
        this.userQueryService = userQueryService;
    }

    /**
     * Handle incoming chat messages from WebSocket clients
     * Endpoint: /app/chat.sendMessage
     */
    @MessageMapping("/chat.sendMessage")
    public void sendMessage(@Payload ChatMessageRequest request) {
        logger.info("Received chat message: {}", request.getContent());

        try {
            // Verify user is authenticated
            if (!authenticationService.isAuthenticated()) {
                throw new AccessDeniedException("User not authenticated");
            }

            // Get authenticated user information
            Long authenticatedUserId = authenticationService.getCurrentUserId().getValue();
            UserDto userDto = userQueryService.findUserById(authenticatedUserId);

            // Verify the sender ID matches the authenticated user (if provided)
            if (request.getSenderId() != null) {
                Long requestSenderId = Long.parseLong(request.getSenderId());
                if (!authenticatedUserId.equals(requestSenderId)) {
                    throw new AccessDeniedException("Cannot send message as different user");
                }
            }

            // Convert request to command
            CreateMessageCommand command = new CreateMessageCommand();
            command.setConversationId(Long.parseLong(request.getConversationId()));
            command.setSenderId(authenticatedUserId);
            command.setContent(request.getContent());
            command.setType(request.getType());

            // Process the message
            chatService.createMessage(command);

            logger.info("Successfully processed message from user: {}", userDto.getName());

        } catch (AccessDeniedException e) {
            logger.warn("Access denied for chat message: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error processing chat message: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process chat message", e);
        }
    }
}