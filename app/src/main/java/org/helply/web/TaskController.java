package org.helply.web;

import org.helply.task.application.TaskQueryService;
import org.helply.task.application.command.DeleteTaskCommand;
import org.helply.task.application.command.SaveTaskCommand;
import org.helply.task.application.query.TaskDto;
import org.helply.web.request.task.CreateTaskRequest;
import org.helply.web.request.task.ModifyTaskRequest;
import org.helply.task.application.TaskApplicationService;
import org.helply.task.domain.TaskCategory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/task")
public class TaskController {

    private final TaskApplicationService taskApplicationService;
    private final TaskQueryService taskQueryService;

    public TaskController(TaskApplicationService taskApplicationService,
                          TaskQueryService taskQueryService) {
        this.taskApplicationService = taskApplicationService;
        this.taskQueryService = taskQueryService;
    }

    @PostMapping
    ResponseEntity<Long> createTask(@RequestBody CreateTaskRequest request) {
        SaveTaskCommand command = request.toCommand();
        Long taskId = taskApplicationService.createTask(command);
        return ResponseEntity.ok(taskId);
    }

    @PutMapping
    ResponseEntity<?> modifyTask(@RequestBody ModifyTaskRequest request) {
        SaveTaskCommand command = request.toCommand();
        taskApplicationService.modifyTask(command);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{taskId}")
    ResponseEntity<TaskDto> getTask(@PathVariable("taskId") long taskId) {
        TaskDto taskDto = taskQueryService.getTaskById(taskId);
        return ResponseEntity.ok(taskDto);
    }

    @GetMapping
    ResponseEntity<List<TaskDto>> getAllTasks() {
        List<TaskDto> taskDtos = taskQueryService.getAllTasks();
        return ResponseEntity.ok(taskDtos);
    }

    @DeleteMapping("/{taskId}")
    ResponseEntity<?> deleteTask(@PathVariable("taskId") long taskId) {
        DeleteTaskCommand command = new DeleteTaskCommand();
        command.setId(taskId);
        taskApplicationService.deleteTask(command);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/category/{category}")
    ResponseEntity<List<TaskDto>> getTasksByCategory(@PathVariable("category") TaskCategory category) {
        List<TaskDto> taskDtos = taskQueryService.getTasksByCategory(category);
        return ResponseEntity.ok(taskDtos);
    }

    @GetMapping("/search")
    ResponseEntity<List<TaskDto>> searchTasks(@RequestParam("keyword") String keyword) {
        List<TaskDto> taskDtos = taskQueryService.searchTasks(keyword);
        return ResponseEntity.ok(taskDtos);
    }

    @GetMapping("/price-range")
    ResponseEntity<List<TaskDto>> getTasksByPriceRange(@RequestParam("minPrice") BigDecimal minPrice,
                                            @RequestParam("maxPrice") BigDecimal maxPrice) {
        List<TaskDto> taskDtos = taskQueryService.getTasksByPriceRange(minPrice, maxPrice);
        return ResponseEntity.ok(taskDtos);
    }

    @GetMapping("/category/{category}/max-price/{maxPrice}")
    ResponseEntity<List<TaskDto>> getTasksByCategoryAndMaxPrice(@PathVariable("category") TaskCategory category,
                                                     @PathVariable("maxPrice") BigDecimal maxPrice) {
        List<TaskDto> taskDtos = taskQueryService.getTasksByCategoryAndMaxPrice(category, maxPrice);
        return ResponseEntity.ok(taskDtos);
    }
}
