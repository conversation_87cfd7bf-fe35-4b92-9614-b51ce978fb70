package org.helply.web.request.user;

import org.helply.user.application.command.ModifyUserCommand;

import java.time.LocalDate;

public class ModifyUserRequest {
    private Long id;
    private String name;
    private LocalDate birthday;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }
    
    public ModifyUserCommand toCommand() {
        ModifyUserCommand command = new ModifyUserCommand();
        command.setId(id);
        command.setBirthday(birthday);
        command.setName(name);
        return command;
    }
}
