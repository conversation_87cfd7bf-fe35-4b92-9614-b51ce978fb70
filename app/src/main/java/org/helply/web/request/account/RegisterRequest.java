package org.helply.web.request.account;

import org.helply.account.application.command.RegisterAccountCommand;
import org.helply.account.domain.AuthProvider;

public class RegisterRequest {
    private String email;
    private String password;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public RegisterAccountCommand toCommand() {
        RegisterAccountCommand command = new RegisterAccountCommand();
        command.setProvider(AuthProvider.LOCAL);
        command.setEmail(email);
        command.setPassword(password);
        return command;
    }
}
