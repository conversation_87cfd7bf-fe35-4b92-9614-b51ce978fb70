package org.helply.web.request.user;

import org.helply.user.application.command.CreateAddressCommand;

public class CreateAddressRequest {
    private String name;
    private String phoneNumber;
    private String countryCode;
    private String country;
    private String state;
    private String city;
    private String postcode;
    private String street;
    private boolean defaultAddress;
    private boolean billAddress;
    
    public CreateAddressCommand toCommand() {
        CreateAddressCommand command = new CreateAddressCommand();
        command.setName(name);
        command.setPhoneNumber(phoneNumber);
        command.setCountryCode(countryCode);
        command.setCountry(country);
        command.setState(state);
        command.setCity(city);
        command.setPostcode(postcode);
        command.setStreet(street);
        command.setDefaultAddress(defaultAddress);
        command.setBillAddress(billAddress);
        return command;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public boolean isDefaultAddress() {
        return defaultAddress;
    }

    public void setDefaultAddress(boolean defaultAddress) {
        this.defaultAddress = defaultAddress;
    }

    public boolean isBillAddress() {
        return billAddress;
    }

    public void setBillAddress(boolean billAddress) {
        this.billAddress = billAddress;
    }
}
