package org.helply.web.request.task;

import org.helply.task.application.command.SaveTaskCommand;
import org.helply.task.domain.TaskCategory;

import java.math.BigDecimal;

public class ModifyTaskRequest {
    private Long id;
    private String title;
    private String description;
    private TaskCategory category;
    private BigDecimal price;
    private Long providerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TaskCategory getCategory() {
        return category;
    }

    public void setCategory(TaskCategory category) {
        this.category = category;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public SaveTaskCommand toCommand() {
        SaveTaskCommand command = new SaveTaskCommand();
        command.setId(id);
        command.setCategory(category);
        command.setDescription(description);
        command.setTitle(title);
        command.setPrice(price);
        command.setProviderId(providerId);
        return command;
    }
}
