package org.helply.web.request.account;

import org.helply.account.application.command.ThirdPartyLoginCommand;
import org.helply.account.domain.AuthProvider;

public class SocialLoginRequest {
    private String token;
    private AuthProvider provider;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public AuthProvider getProvider() {
        return provider;
    }

    public void setProvider(AuthProvider provider) {
        this.provider = provider;
    }

    public ThirdPartyLoginCommand toCommand() {
        ThirdPartyLoginCommand command = new ThirdPartyLoginCommand();
        command.setProvider(provider);
        command.setToken(token);
        return command;
    }
}
