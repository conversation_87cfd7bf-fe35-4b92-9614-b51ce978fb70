package org.helply.web.request.account;

import org.helply.account.application.command.LocalLoginCommand;

public class LoginRequest {
    private String email;
    private String password;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public LocalLoginCommand toCommand() {
        LocalLoginCommand command = new LocalLoginCommand();
        command.setEmail(email);
        command.setPassword(password);
        return command;
    }
}
