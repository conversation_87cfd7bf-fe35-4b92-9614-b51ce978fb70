package org.helply.web.config;

import org.helply.web.handler.WebSocketJwtAuthenticationHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private final WebSocketJwtAuthenticationHandler webSocketJwtAuthenticationHandler;

    public WebSocketConfig(WebSocketJwtAuthenticationHandler webSocketJwtAuthenticationHandler) {
        this.webSocketJwtAuthenticationHandler = webSocketJwtAuthenticationHandler;
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // Enable a simple memory-based message broker to carry messages
        // back to client on destinations prefixed with "/topic"
        config.enableSimpleBroker("/topic", "/queue");

        // Prefix for messages FROM client TO server
        config.setApplicationDestinationPrefixes("/app");

        // Prefix for user-specific destinations
        config.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Register STOMP endpoint that clients will use to connect
        registry.addEndpoint("/ws")
            .setAllowedOriginPatterns("*") // Configure properly for production
            .withSockJS(); // Enable SockJS fallback options
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        // Add JWT authentication interceptor for WebSocket messages
        registration.interceptors(webSocketJwtAuthenticationHandler);
    }
}