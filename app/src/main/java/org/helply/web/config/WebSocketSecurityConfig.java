package org.helply.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.config.annotation.web.socket.EnableWebSocketSecurity;
import org.springframework.security.messaging.access.intercept.MessageMatcherDelegatingAuthorizationManager;

/**
 * WebSocket Security Configuration
 * Secures WebSocket endpoints and message destinations
 */
@Configuration
@EnableWebSocketSecurity
public class WebSocketSecurityConfig {

    @Bean
    AuthorizationManager<Message<?>> authorizationManager(MessageMatcherDelegatingAuthorizationManager.Builder messages) {
        messages
            // Allow connection to WebSocket endpoint
            .simpDestMatchers("/ws/**").permitAll()

            // Require authentication for sending messages
            .simpDestMatchers("/app/**").authenticated()

            // Allow subscription to topics (will be filtered by user)
            .simpSubscribeDestMatchers("/topic/**", "/queue/**", "/user/**").authenticated()

            // Any other message requires authentication
            .anyMessage().authenticated();
        
        return messages.build();
    }
}
