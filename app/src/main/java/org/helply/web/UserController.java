package org.helply.web;

import org.helply.user.application.UserQueryService;
import org.helply.user.application.command.CreateAddressCommand;
import org.helply.user.application.command.DeleteAddressCommand;
import org.helply.user.application.command.ModifyUserCommand;
import org.helply.user.application.query.UserAddressDto;
import org.helply.web.request.user.CreateAddressRequest;
import org.helply.web.request.user.ModifyUserRequest;
import org.helply.user.application.UserApplicationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequestMapping("user")
@RestController
public class UserController {

    private final UserApplicationService userApplicationService;
    private final UserQueryService userQueryService;

    public UserController(UserApplicationService userApplicationService, UserQueryService userQueryService) {
        this.userApplicationService = userApplicationService;
        this.userQueryService = userQueryService;
    }

    @PutMapping
    ResponseEntity<?> updateUser(@RequestBody ModifyUserRequest request) {
        ModifyUserCommand command = request.toCommand();
        userApplicationService.modifyUser(command);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{userId}")
    ResponseEntity<UserAddressDto> getUser(@PathVariable("userId") long userId) {
        UserAddressDto dto = userQueryService.findUserWithAddress(userId);
        return ResponseEntity.ok(dto);
    }
    
    @PostMapping("/{userId}/address")
    ResponseEntity<?> addAddress(@PathVariable("userId") long userId, @RequestBody CreateAddressRequest request) {
        CreateAddressCommand command = request.toCommand();
        command.setUserId(userId);
        userApplicationService.createUserAddress(command);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("{userId}/address/{addressId}")
    ResponseEntity<?> deleteAddress(@PathVariable("userId") long userId, @PathVariable("addressId") long addressId) {
        DeleteAddressCommand command = new DeleteAddressCommand();
        command.setUserId(userId);
        command.setAddressId(addressId);
        userApplicationService.deleteUserAddress(command);
        return ResponseEntity.ok().build();
    }
}
