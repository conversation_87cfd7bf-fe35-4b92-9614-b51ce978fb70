package org.helply.web.handler;

import org.helply.chat.application.OnlineUserService;
import org.helply.chat.application.query.ChatMessageDto;
import org.helply.chat.domain.MessageType;
import org.helply.user.domain.UserId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.time.LocalDateTime;

@Component
public class WebSocketEventHandler {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventHandler.class);

    private final SimpMessagingTemplate messagingTemplate;
    private final OnlineUserService onlineUserService;

    public WebSocketEventHandler(SimpMessagingTemplate messagingTemplate, OnlineUserService onlineUserService) {
        this.messagingTemplate = messagingTemplate;
        this.onlineUserService = onlineUserService;
    }

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());

        // Extract user ID from session attributes
        String userIdStr = (String) headerAccessor.getSessionAttributes().get("userId");
        if (userIdStr != null) {
            try {
                UserId userId = UserId.of(Long.parseLong(userIdStr));
                onlineUserService.markUserOnline(userId);
                logger.info("User {} marked as online", userId.getValue());
            } catch (NumberFormatException e) {
                logger.warn("Invalid user ID in session: {}", userIdStr);
            }
        }

        logger.info("Received a new web socket connection");
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());

        // Extract user ID from session attributes
        String userIdStr = (String) headerAccessor.getSessionAttributes().get("userId");
        if (userIdStr != null) {
            try {
                UserId userId = UserId.of(Long.parseLong(userIdStr));
                onlineUserService.markUserOffline(userId);
                logger.info("User {} marked as offline", userId.getValue());

                // Notify other users that this user left
                ChatMessageDto chatMessage = new ChatMessageDto();
                chatMessage.setType(MessageType.LEAVE);
                chatMessage.setSenderId(userId.getValue());
                chatMessage.setCreateTime(LocalDateTime.now());

                messagingTemplate.convertAndSend("/topic/public", chatMessage);
            } catch (NumberFormatException e) {
                logger.warn("Invalid user ID in session: {}", userIdStr);
            }
        }
    }
}