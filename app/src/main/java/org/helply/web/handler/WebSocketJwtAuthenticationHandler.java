package org.helply.web.handler;

import org.helply.account.domain.Account;
import org.helply.account.domain.AccountRepository;
import org.helply.account.domain.Authenticator;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.shared.domain.Email;
import org.helply.account.infrastructure.security.AccountUserDetails;
import org.helply.account.infrastructure.security.JwtAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * WebSocket interceptor for JWT authentication
 * Authenticates users when they connect to WebSocket and send messages
 */
@Component
public class WebSocketJwtAuthenticationHandler implements ChannelInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketJwtAuthenticationHandler.class);

    private final Authenticator authenticator;
    private final AccountRepository accountRepository;

    public WebSocketJwtAuthenticationHandler(Authenticator authenticator, AccountRepository accountRepository) {
        this.authenticator = authenticator;
        this.accountRepository = accountRepository;
    }

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
            // Handle WebSocket connection
            authenticateWebSocketConnection(accessor);
        } else if (accessor != null && accessor.getUser() == null) {
            // For other commands, try to authenticate if not already authenticated
            authenticateWebSocketMessage(accessor);
        }
        
        return message;
    }

    private void authenticateWebSocketConnection(StompHeaderAccessor accessor) {
        try {
            String jwt = getJwtFromHeaders(accessor);
            
            if (StringUtils.hasText(jwt) && authenticator.verify(jwt)) {
                Email email = authenticator.getEmailFromToken(jwt);
                Account account = accountRepository.findByEmail(email)
                    .orElseThrow(() -> new AccountNotFoundException(email));

                // Set Spring Security context
                AccountUserDetails userDetails = new AccountUserDetails(account);
                JwtAuthenticationToken authToken = new JwtAuthenticationToken(
                    jwt, 
                    account, 
                    userDetails.getAuthorities()
                );
                
                SecurityContextHolder.setContext(new SecurityContextImpl(authToken));
                
                // Set user in WebSocket session
                accessor.setUser(authToken);
                
                // Store user information in session attributes for later use
                accessor.getSessionAttributes().put("userId", account.getUserId().getValue().toString());
                accessor.getSessionAttributes().put("userEmail", email.getValue());
                
                logger.info("WebSocket authentication successful for user: {}", email.getValue());
            } else {
                logger.warn("WebSocket authentication failed - invalid or missing JWT token");
            }
        } catch (Exception e) {
            logger.error("Error during WebSocket authentication", e);
        }
    }

    private void authenticateWebSocketMessage(StompHeaderAccessor accessor) {
        // For subsequent messages, we can rely on the session-stored authentication
        // or re-authenticate if needed
        if (accessor.getSessionAttributes() != null) {
            String userId = (String) accessor.getSessionAttributes().get("userId");
            String userEmail = (String) accessor.getSessionAttributes().get("userEmail");
            
            if (userId != null && userEmail != null) {
                // User is already authenticated in this session
                logger.debug("Using existing WebSocket session authentication for user: {}", userEmail);
                return;
            }
        }
        
        // Try to authenticate with JWT from headers
        authenticateWebSocketConnection(accessor);
    }

    private String getJwtFromHeaders(StompHeaderAccessor accessor) {
        // Try to get JWT from Authorization header
        String authHeader = accessor.getFirstNativeHeader("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // Try to get JWT from custom header (for WebSocket clients that can't set Authorization header)
        String jwtHeader = accessor.getFirstNativeHeader("X-JWT-Token");
        if (StringUtils.hasText(jwtHeader)) {
            return jwtHeader;
        }
        
        return null;
    }
}
