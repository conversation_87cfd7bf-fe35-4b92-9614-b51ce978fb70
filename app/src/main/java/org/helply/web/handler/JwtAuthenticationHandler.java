package org.helply.web.handler;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.helply.account.domain.Account;
import org.helply.account.domain.AccountRepository;
import org.helply.account.domain.Authenticator;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.shared.domain.Email;
import org.helply.account.infrastructure.security.AccountUserDetails;
import org.helply.account.infrastructure.security.JwtAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT Authentication Filter for Spring Security
 * Integrates with existing JWT authentication logic
 */
@Component
public class JwtAuthenticationHandler extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationHandler.class);

    private final Authenticator authenticator;
    private final AccountRepository accountRepository;

    public JwtAuthenticationHandler(Authenticator authenticator, AccountRepository accountRepository) {
        this.authenticator = authenticator;
        this.accountRepository = accountRepository;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        logger.debug("JWT Filter - Processing request: {} {}", request.getMethod(), request.getRequestURI());

        try {
            String jwt = getJwtFromRequest(request);
            
            if (StringUtils.hasText(jwt) && authenticator.verify(jwt)) {
                Email email = authenticator.getEmailFromToken(jwt);
                logger.debug("JWT verified for user: {}", email.getValue());

                Account account = accountRepository.findByEmail(email)
                    .orElseThrow(() -> new AccountNotFoundException(email));

                // Set Spring Security context
                AccountUserDetails userDetails = new AccountUserDetails(account);
                JwtAuthenticationToken authToken = new JwtAuthenticationToken(
                    jwt, 
                    account, 
                    userDetails.getAuthorities()
                );

                SecurityContextHolder.setContext(new SecurityContextImpl(authToken));
                logger.debug("Authentication successful for user: {}", email.getValue());
            }
        } catch (Exception ex) {
            logger.error("Could not set user authentication in security context", ex);
            // Clear both security contexts on error
            SecurityContextHolder.clearContext();
        }

        filterChain.doFilter(request, response);
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        
        // Skip filter for public endpoints
        return path.startsWith("/auth/") || 
               path.startsWith("/health/") || 
               path.startsWith("/h2-console/") ||
               path.startsWith("/error");
    }
}
