package org.helply.web.handler;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.helply.account.domain.Account;
import org.helply.account.domain.AccountRepository;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.account.domain.Authenticator;
import org.helply.account.infrastructure.security.SecurityContext;
import org.helply.account.infrastructure.security.SecurityContextHolder;
import org.helply.shared.domain.Email;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class JwtAuthenticationHandler implements HandlerInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(JwtAuthenticationHandler.class);

    private final Authenticator authenticator;

    private final AccountRepository accountRepository;

    public JwtAuthenticationHandler(Authenticator authenticator, AccountRepository accountRepository) {
        this.authenticator = authenticator;
        this.accountRepository = accountRepository;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        LOGGER.info("JwtAuthenticationInterceptor - Processing request: {} {}", request.getMethod(), request.getRequestURI());
        LOGGER.info("JwtAuthenticationInterceptor - Context path: {}", request.getContextPath());
        LOGGER.info("JwtAuthenticationInterceptor - Servlet path: {}", request.getServletPath());

        try {
            String jwt = getJwtFromRequest(request);
            LOGGER.info("JwtAuthenticationInterceptor - JWT token present: {}", StringUtils.hasText(jwt));

            if (StringUtils.hasText(jwt) && authenticator.verify(jwt)) {
                Email email = authenticator.getEmailFromToken(jwt);
                LOGGER.info("JwtAuthenticationInterceptor - JWT verified for user: {}", email.getValue());

                Account account = accountRepository.findByEmail(email).orElseThrow(() -> new AccountNotFoundException(email));
                SecurityContextHolder.setContext(new SecurityContext(account));
                LOGGER.info("JwtAuthenticationInterceptor - Authentication successful");
                return true;
            }
        } catch (Exception ex) {
            LOGGER.error("Could not set user authentication in security context", ex);
        }

        LOGGER.warn("JwtAuthenticationInterceptor - Authentication failed, returning 401");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED); // 401
        response.setContentType("application/json");
        response.getWriter().write("{\"error\": \"Unauthorized\"}");
        return false;
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
