package org.helply.web.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionControllerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExceptionControllerHandler.class);

    @ExceptionHandler(RuntimeException.class)
    ResponseEntity<?> defaultHandler(RuntimeException e) {
        LOGGER.error(e.getMessage());
        return ResponseEntity.internalServerError().build();
    }

    @ExceptionHandler(AccessDeniedException.class) 
    ResponseEntity<?> build(AccessDeniedException e) {
        LOGGER.error(e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }
}
