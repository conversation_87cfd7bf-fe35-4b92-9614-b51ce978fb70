package org.helply.web.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionControllerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExceptionControllerHandler.class);

    @ExceptionHandler(RuntimeException.class)
    ResponseEntity<?> defaultHandler(RuntimeException e) {
        LOGGER.error(e.getMessage());
        return ResponseEntity.internalServerError().build();
    }


}
