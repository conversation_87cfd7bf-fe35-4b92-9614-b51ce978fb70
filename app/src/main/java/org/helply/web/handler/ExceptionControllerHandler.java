package org.helply.web.handler;

import org.helply.account.exception.PasswordNotMatchedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionControllerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExceptionControllerHandler.class);

    @ExceptionHandler(AccessDeniedException.class) 
    ResponseEntity<?> handle(AccessDeniedException e) {
        LOGGER.error(e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    @ExceptionHandler(PasswordNotMatchedException.class)
    ResponseEntity<?> handle(PasswordNotMatchedException e) {
        LOGGER.error(e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }
    
    @ExceptionHandler(Exception.class)
    ResponseEntity<?> defaultHandler(Exception e) {
        LOGGER.error("default Handler: {}", e.getMessage());
        return ResponseEntity.internalServerError().build();
    }
}
