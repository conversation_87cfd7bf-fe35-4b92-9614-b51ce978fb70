package org.helply.web;

import org.helply.account.application.AccountQueryService;
import org.helply.account.application.command.LocalLoginCommand;
import org.helply.account.application.command.RegisterAccountCommand;
import org.helply.account.application.command.ThirdPartyLoginCommand;
import org.helply.account.application.query.AccessTokenDto;
import org.helply.account.application.query.AccountDto;
import org.helply.account.exception.AccountNotFoundException;
import org.helply.web.request.account.LoginRequest;
import org.helply.web.request.account.RegisterRequest;
import org.helply.web.request.account.SocialLoginRequest;
import org.helply.account.application.AuthApplicationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
public class AuthController {
    private final AuthApplicationService authApplicationService;
    private final AccountQueryService accountQueryService;

    public AuthController(AuthApplicationService authApplicationService, AccountQueryService accountQueryService) {
        this.authApplicationService = authApplicationService;
        this.accountQueryService = accountQueryService;
    }

    @PostMapping("/login")
    ResponseEntity<AccessTokenDto> login(@RequestBody LoginRequest request) {
        LocalLoginCommand command = request.toCommand();
        AccessTokenDto dto = authApplicationService.login(command);
        return ResponseEntity.ok(dto);
    }

    @PostMapping("/register")
    ResponseEntity<?> register(@RequestBody RegisterRequest request) {
        RegisterAccountCommand command = request.toCommand();
        authApplicationService.register(command);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/sso")
    ResponseEntity<AccessTokenDto> socialLogin(@RequestBody SocialLoginRequest request) {
        ThirdPartyLoginCommand command = request.toCommand();
        AccessTokenDto dto = authApplicationService.sso(command);
        return ResponseEntity.ok(dto);
    }

    @GetMapping("/precheck")
    ResponseEntity<AccountDto> precheck(@RequestParam("email") String email) {
        try {
            AccountDto dto = accountQueryService.getAccountByEmail(email);
            return ResponseEntity.ok(dto);
        } catch (AccountNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
