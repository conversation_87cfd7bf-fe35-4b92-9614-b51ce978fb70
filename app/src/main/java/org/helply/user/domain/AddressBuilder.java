package org.helply.user.domain;

import org.helply.shared.domain.PhoneNumber;

import java.time.LocalDateTime;

public final class AddressBuilder {

    private AddressId id;
    private Name recipient;
    private PhoneNumber phoneNumber;
    private String country;
    private String state;
    private String city;
    private String postcode;
    private String street;
    private boolean defaultAddress;
    private boolean billAddress;
    private LocalDateTime lastModifyTime;

    private AddressBuilder() {
    }

    public static AddressBuilder anAddress() {
        return new AddressBuilder();
    }

    public AddressBuilder withId(AddressId id) {
        this.id = id;
        return this;
    }

    public AddressBuilder withRecipient(Name recipient) {
        this.recipient = recipient;
        return this;
    }

    public AddressBuilder withPhoneNumber(PhoneNumber phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public AddressBuilder withCountry(String country) {
        this.country = country;
        return this;
    }

    public AddressBuilder withState(String state) {
        this.state = state;
        return this;
    }

    public AddressBuilder withCity(String city) {
        this.city = city;
        return this;
    }

    public AddressBuilder withPostcode(String postcode) {
        this.postcode = postcode;
        return this;
    }

    public AddressBuilder withStreet(String street) {
        this.street = street;
        return this;
    }

    public AddressBuilder withDefaultAddress(boolean defaultAddress) {
        this.defaultAddress = defaultAddress;
        return this;
    }

    public AddressBuilder withBillAddress(boolean billAddress) {
        this.billAddress = billAddress;
        return this;
    }

    public AddressBuilder withLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
        return this;
    }

    public Address build() {
        return new Address(id, recipient, phoneNumber, country, state, city, postcode, street, defaultAddress, billAddress, lastModifyTime);
    }

}
