package org.helply.user.domain;

import org.helply.shared.domain.Email;

import java.time.LocalDate;
import java.util.List;

public final class UserBuilder {
    private UserId id;
    private Email email;
    private Name name;
    private LocalDate birthday;
    private List<Address> addresses;

    private UserBuilder() {
    }

    public static UserBuilder anUser() {
        return new UserBuilder();
    }

    public UserBuilder withId(UserId id) {
        this.id = id;
        return this;
    }

    public UserBuilder withEmail(Email email) {
        this.email = email;
        return this;
    }

    public UserBuilder withName(Name name) {
        this.name = name;
        return this;
    }

    public UserBuilder withBirthday(LocalDate birthday) {
        this.birthday = birthday;
        return this;
    }

    public UserBuilder withAddresses(List<Address> addresses) {
        this.addresses = addresses;
        return this;
    }

    public User build() {
        User user = new User(id, name, birthday, email, addresses);
        return user;
    }
}
