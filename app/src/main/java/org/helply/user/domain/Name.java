package org.helply.user.domain;

import org.apache.commons.lang3.StringUtils;
import org.helply.shared.domain.ValueObject;

public class Name extends ValueObject {
    
    private final String value;

    public Name(String value) {
        this.value = value;
        validate();
    }

    public String getValue() {
        return value;
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] { value };
    }

    @Override
    protected void validate() {
        if (StringUtils.isBlank(value)) {
            throw new IllegalArgumentException("firstName and lastName cannot be empty!");
        }
    }
    
    public static Name of(String name) {
        return new Name(name);
    }

    public static Name of(String... names) {
        if (names.length == 0) {
            throw new IllegalArgumentException();
        }
        StringBuilder sb = new StringBuilder();
        for (String name : names) {
            if (name != null) {
                sb.append(name).append(" ");
            }
        }
        sb.deleteCharAt(sb.length() - 1);
        return new Name(sb.toString());
    }
}
