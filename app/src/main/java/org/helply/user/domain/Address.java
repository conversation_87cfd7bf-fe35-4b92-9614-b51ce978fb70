package org.helply.user.domain;

import org.helply.shared.domain.DomainEntity;
import org.helply.shared.domain.PhoneNumber;

import java.time.LocalDateTime;

public class Address extends DomainEntity<AddressId> {

    private final AddressId id;
    private Name recipient;
    private PhoneNumber phoneNumber;
    private String country;
    private String state;
    private String city;
    private String postcode;
    private String street;
    private boolean defaultAddress;
    private boolean billAddress;
    private LocalDateTime lastModifyTime;

    public Address(AddressId id, Name recipient, PhoneNumber phoneNumber, String country, String state, String city, String postcode, String street, boolean defaultAddress, boolean billAddress, LocalDateTime lastModifyTime) {
        this.id = id;
        this.recipient = recipient;
        this.phoneNumber = phoneNumber;
        this.country = country;
        this.state = state;
        this.city = city;
        this.postcode = postcode;
        this.street = street;
        this.defaultAddress = defaultAddress;
        this.billAddress = billAddress;
        this.lastModifyTime = lastModifyTime;
    }

    public Name getRecipient() {
        return recipient;
    }

    public PhoneNumber getPhoneNumber() {
        return phoneNumber;
    }

    public String getCountry() {
        return country;
    }

    public String getState() {
        return state;
    }

    public String getCity() {
        return city;
    }

    public String getPostcode() {
        return postcode;
    }

    public String getStreet() {
        return street;
    }

    public boolean isDefaultAddress() {
        return defaultAddress;
    }

    public boolean isBillAddress() {
        return billAddress;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    @Override
    public AddressId getId() {
        return id;
    }
}
