package org.helply.user.domain;

import org.helply.shared.domain.AggregateRoot;
import org.helply.shared.domain.Email;
import org.helply.user.exception.AddressAlreadyExistedException;
import org.helply.user.exception.AddressNotFoundException;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class User extends AggregateRoot<UserId> {

    private final UserId id;
    private Email email;
    private Name name;
    private LocalDate birthday;
    private List<Address> addresses;

    public User(UserId id, Name name, LocalDate birthday, Email email, List<Address> addresses) {
        this.id = id;
        this.name = name;
        this.birthday = birthday;
        this.email = email;
        this.addresses = addresses;
    }

    public void addAddress(Address newAddress) {
        boolean existed = addresses.stream().anyMatch(address -> Objects.equals(address, newAddress));
        if (existed) {
            throw new AddressAlreadyExistedException(newAddress.getId());
        }

        addresses.add(newAddress);
    }

    public void deleteAddress(AddressId addressId) {
        boolean success = addresses.removeIf(address -> Objects.equals(address.getId(), addressId));
        if (!success) {
            throw new AddressNotFoundException(addressId);
        }
    }

    public void deleteAllAddresses() {
        if (!addresses.isEmpty()) {
            addresses.clear();
        }
    }
    
    public void updateBasicInfo(Name name, LocalDate birthday) {
        this.name = name;
        this.birthday = birthday;
    }
    

    @Override
    public UserId getId() {
        return id;
    }

    public Name getName() {
        return name;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public Email getEmail() {
        return email;
    }

    public List<Address> getAddresses() {
        return Collections.unmodifiableList(addresses);
    }

}
