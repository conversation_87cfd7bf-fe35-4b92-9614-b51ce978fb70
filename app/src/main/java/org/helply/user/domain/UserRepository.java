package org.helply.user.domain;

import org.helply.shared.domain.Email;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface UserRepository {

    User save(User user);

    void delete(UserId userId);

    Optional<User> findById(UserId userId);

    Optional<User> findByIdWithAddresses(UserId identity);

    Optional<User> findByEmail(Email email);
    
    List<User> findByIds(Set<UserId> userIds);
}
