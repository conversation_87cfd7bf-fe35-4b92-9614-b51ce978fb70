package org.helply.user.exception;

import org.helply.shared.domain.Email;
import org.helply.user.domain.UserId;

public class UserNotFoundException extends RuntimeException {
    public UserNotFoundException(UserId userId) {
        super("User not found with id: " + userId.getValue());
    }

    public UserNotFoundException(Email email) {
        super("User not found with email: " + email.getValue());
    }
}
