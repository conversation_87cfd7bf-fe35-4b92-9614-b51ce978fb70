package org.helply.user.application.query;

import org.helply.user.domain.User;

import java.util.List;

public class UserAddressDto {
    UserDto user;
    List<AddressDto> addresses;
    
    public static UserAddressDto fromDomain(User user) {
        UserAddressDto dto = new UserAddressDto();
        dto.user = UserDto.fromDomain(user);
        dto.addresses = user.getAddresses().stream().map(AddressDto::fromDomain).toList();
        return dto;
    }

    public UserDto getUser() {
        return user;
    }

    public List<AddressDto> getAddresses() {
        return addresses;
    }
}
