package org.helply.user.application;

import org.helply.shared.domain.Email;
import org.helply.user.application.query.UserAddressDto;
import org.helply.user.application.query.UserDto;
import org.helply.user.domain.User;
import org.helply.user.domain.UserId;
import org.helply.user.domain.UserRepository;
import org.helply.user.exception.UserNotFoundException;
import org.springframework.stereotype.Component;

@Component
public class UserQueryService {
    private final UserRepository userRepository;

    public UserQueryService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    public UserDto findUserByEmail(String rawEmail) {
        Email email = Email.of(rawEmail);
        User user = userRepository.findByEmail(email).orElseThrow(() -> new UserNotFoundException(email));
        return UserDto.fromDomain(user);
    }

    public UserDto findUserById(Long id) {
        UserId userId = UserId.of(id);
        User user = userRepository.findById(userId).orElseThrow(() -> new UserNotFoundException(userId));
        return UserDto.fromDomain(user);
    }
    
    public UserAddressDto findUserWithAddress(Long id) {
        UserId userId = UserId.of(id);
        User user = userRepository.findByIdWithAddresses(userId).orElseThrow(() -> new UserNotFoundException(userId));
        return UserAddressDto.fromDomain(user);
    }
}
