package org.helply.user.application.command;

import org.helply.shared.domain.PhoneNumber;
import org.helply.user.domain.Address;
import org.helply.user.domain.AddressBuilder;
import org.helply.user.domain.Name;

public class CreateAddressCommand {
    private Long userId;
    private String name;
    private String phoneNumber;
    private String countryCode;
    private String country;
    private String state;
    private String city;
    private String postcode;
    private String street;
    private boolean defaultAddress;
    private boolean billAddress;
    
    public Address toDomain() {
        return AddressBuilder.anAddress()
                .withId(null)
                .withRecipient(name != null ? Name.of(name) : null)
                .withPhoneNumber(phoneNumber != null && countryCode != null ?
                    new PhoneNumber(countryCode, phoneNumber) : null)
                .withCountry(country)
                .withState(state)
                .withCity(city)
                .withPostcode(postcode)
                .withStreet(street)
                .withDefaultAddress(defaultAddress)
                .withBillAddress(billAddress)
                .build();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public boolean isDefaultAddress() {
        return defaultAddress;
    }

    public void setDefaultAddress(boolean defaultAddress) {
        this.defaultAddress = defaultAddress;
    }

    public boolean isBillAddress() {
        return billAddress;
    }

    public void setBillAddress(boolean billAddress) {
        this.billAddress = billAddress;
    }
}
