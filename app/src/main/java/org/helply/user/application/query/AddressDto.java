package org.helply.user.application.query;

import org.helply.user.domain.Address;

public class AddressDto {
    private Long id;
    private String name;
    private String phoneNumber;
    private String countryCode;
    private String country;
    private String state;
    private String city;
    private String postcode;
    private String street;
    private boolean defaultAddress;
    private boolean billAddress;
    
    public static AddressDto fromDomain(Address address) {
        if (address == null) {
            return null;
        }

        AddressDto dto = new AddressDto();

        // Map ID with null check
        if (address.getId() != null) {
            dto.id = address.getId().getValue();
        }

        // Map recipient name with null check
        if (address.getRecipient() != null) {
            dto.name = address.getRecipient().getValue();
        }

        // Map phone number with null check
        if (address.getPhoneNumber() != null) {
            dto.phoneNumber = address.getPhoneNumber().getPhoneNumber();
            dto.countryCode = address.getPhoneNumber().getCountryCode();
        }

        // Map address fields (these are primitive strings, but still check for null)
        dto.country = address.getCountry();
        dto.state = address.getState();
        dto.city = address.getCity();
        dto.postcode = address.getPostcode();
        dto.street = address.getStreet();

        // Map boolean fields (primitives, no null check needed)
        dto.defaultAddress = address.isDefaultAddress();
        dto.billAddress = address.isBillAddress();

        return dto;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public String getCountry() {
        return country;
    }

    public String getState() {
        return state;
    }

    public String getCity() {
        return city;
    }

    public String getPostcode() {
        return postcode;
    }

    public String getStreet() {
        return street;
    }

    public boolean isDefaultAddress() {
        return defaultAddress;
    }

    public boolean isBillAddress() {
        return billAddress;
    }
}
