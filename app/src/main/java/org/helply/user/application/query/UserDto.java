package org.helply.user.application.query;

import org.helply.user.domain.User;

import java.time.LocalDate;

public class UserDto {
    private Long id;
    private String email;
    private String name;
    private LocalDate birthday;
    
    public static UserDto fromDomain(User user) {
        if (user == null) {
            return null;
        }

        UserDto dto = new UserDto();

        // Map ID with null check
        if (user.getId() != null) {
            dto.id = user.getId().getValue();
        }

        // Map email with null check
        if (user.getEmail() != null) {
            dto.email = user.getEmail().getValue();
        }

        // Map name with null check
        if (user.getName() != null) {
            dto.name = user.getName().getValue();
        }

        // Map birthday (LocalDate can be null)
        dto.birthday = user.getBirthday();

        return dto;
    }

    public Long getId() {
        return id;
    }

    public String getEmail() {
        return email;
    }

    public String getName() {
        return name;
    }

    public LocalDate getBirthday() {
        return birthday;
    }
}
