package org.helply.user.application;

import org.helply.user.application.command.CreateAddressCommand;
import org.helply.user.application.command.DeleteAddressCommand;
import org.helply.user.application.command.ModifyUserCommand;
import org.helply.user.domain.*;
import org.helply.user.exception.UserNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class UserApplicationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserApplicationService.class);

    private final UserRepository userRepository;

    public UserApplicationService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    public void modifyUser(ModifyUserCommand command) {
        UserId userId = UserId.of(command.getId());
        User user = userRepository.findById(userId).orElseThrow(() -> new UserNotFoundException(userId));
        user.updateBasicInfo(Name.of(command.getName()), command.getBirthday());
        
        userRepository.save(user);
    }

    public void createUserAddress(CreateAddressCommand command) {
        UserId userId = UserId.of(command.getUserId());
        Address address = command.toDomain();
        User user = userRepository.findByIdWithAddresses(userId).orElseThrow(() -> new UserNotFoundException(userId));
        user.addAddress(address);

        userRepository.save(user);
    }

    public void deleteUserAddress(DeleteAddressCommand command) {
        UserId userId = UserId.of(command.getUserId());
        AddressId addressId = AddressId.of(command.getAddressId());
        User user = userRepository.findByIdWithAddresses(userId).orElseThrow(() -> new UserNotFoundException(userId));
        user.deleteAddress(addressId);

        userRepository.save(user);
    }

    public void deleteAllUserAddresses(DeleteAddressCommand command) {
        UserId userId = UserId.of(command.getUserId());
        User user = userRepository.findByIdWithAddresses(userId).orElseThrow(() -> new UserNotFoundException(userId));
        user.deleteAllAddresses();
        userRepository.save(user);
    }
}
