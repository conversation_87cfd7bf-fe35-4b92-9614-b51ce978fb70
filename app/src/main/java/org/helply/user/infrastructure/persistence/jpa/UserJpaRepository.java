package org.helply.user.infrastructure.persistence.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserJpaRepository extends JpaRepository<UserJpaEntity, Long> {

    @Query("SELECT u FROM UserJpaEntity u LEFT JOIN FETCH u.addresses WHERE u.id = :id")
    Optional<UserJpaEntity> findByIdWithAddresses(@Param("id") Long id);

    Optional<UserJpaEntity> findByEmail(String email);
    
    @Query("SELECT u FROM UserJpaEntity u WHERE u.id IN :ids")
    List<UserJpaEntity> findByIds(@Param("ids") Set<Long> ids);
}
