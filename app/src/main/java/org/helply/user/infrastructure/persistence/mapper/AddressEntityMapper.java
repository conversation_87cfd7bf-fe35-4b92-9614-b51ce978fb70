package org.helply.user.infrastructure.persistence.mapper;

import org.helply.user.domain.AddressBuilder;
import org.helply.user.domain.Address;
import org.helply.shared.domain.PhoneNumber;
import org.helply.user.domain.Name;
import org.helply.user.infrastructure.persistence.jpa.AddressJpaEntity;
import org.springframework.stereotype.Component;

@Component
public class AddressEntityMapper {

    public Address toDomain(AddressJpaEntity entity) {
        return AddressBuilder.anAddress()
            .withRecipient(new Name(entity.getName()))
            .withPhoneNumber(new PhoneNumber(entity.getCountryCode(), entity.getPhoneNumber()))
            .build();
    }

    public AddressJpaEntity toEntity(Address address) {
        AddressJpaEntity entity = new AddressJpaEntity();
        if (address.getId() != null) {
            entity.setId(address.getId().getValue());
        }
        entity.setCountry(address.getCountry());
        entity.setState(address.getState());
        entity.setCity(address.getCity());
        entity.setPostcode(address.getPostcode());
        entity.setStreet(address.getStreet());

        Name recipient = address.getRecipient();
        entity.setName(recipient.getValue());

        PhoneNumber phoneNumber = address.getPhoneNumber();
        entity.setCountryCode(phoneNumber.getCountryCode());
        entity.setPhoneNumber(phoneNumber.getPhoneNumber());

        entity.setBillAddress(address.isBillAddress());
        entity.setDefaultAddress(address.isDefaultAddress());
        return entity;
    }
}
