package org.helply.user.infrastructure.persistence;

import org.helply.user.domain.User;
import org.helply.user.domain.UserRepository;
import org.helply.shared.domain.Email;
import org.helply.user.domain.UserId;
import org.helply.user.infrastructure.persistence.jpa.UserJpaEntity;
import org.helply.user.infrastructure.persistence.jpa.UserJpaRepository;
import org.helply.user.infrastructure.persistence.mapper.UserEntityMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class UserRepositoryImpl implements UserRepository {

    private final UserEntityMapper userEntityMapper;
    private final UserJpaRepository userJpaRepository;

    public UserRepositoryImpl(UserEntityMapper userEntityMapper, UserJpaRepository userJpaRepository) {
        this.userEntityMapper = userEntityMapper;
        this.userJpaRepository = userJpaRepository;
    }

    @Override
    public User save(User user) {
        UserJpaEntity userEntity = userEntityMapper.toEntity(user);
        UserJpaEntity saved = userJpaRepository.save(userEntity);
        return userEntityMapper.toDomain(saved);
    }

    @Override
    public void delete(UserId userId) {
        userJpaRepository.deleteById(userId.getValue());
    }

    @Override
    public Optional<User> findById(UserId userId) {
        Optional<UserJpaEntity> userEntity = userJpaRepository.findById(userId.getValue());
        return userEntity.map(userEntityMapper::toDomain);
    }

    @Override
    public Optional<User> findByIdWithAddresses(UserId userId) {
        return userJpaRepository.findByIdWithAddresses(userId.getValue())
            .map(userEntityMapper::toDomain);
    }

    @Override
    public Optional<User> findByEmail(Email email) {
        return userJpaRepository.findByEmail(email.getValue())
            .map(userEntityMapper::toDomain);
    }

    @Override
    public List<User> findByIds(Set<UserId> userIds) {
        Set<Long> ids = userIds.stream().map(UserId::getValue).collect(Collectors.toSet());
        return userJpaRepository.findByIds(ids)
            .stream().map(userEntityMapper::toDomain)
            .toList();
    }
}
