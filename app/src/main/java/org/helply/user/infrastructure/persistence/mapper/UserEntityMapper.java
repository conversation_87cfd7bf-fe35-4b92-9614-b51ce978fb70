package org.helply.user.infrastructure.persistence.mapper;

import org.helply.shared.domain.Email;
import org.helply.user.domain.*;
import org.helply.user.infrastructure.persistence.jpa.AddressJpaEntity;
import org.helply.user.infrastructure.persistence.jpa.UserJpaEntity;
import org.helply.shared.infrastructure.util.SafeLazyLoader;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class UserEntityMapper {

    private final AddressEntityMapper addressEntityMapper;
    private final SafeLazyLoader safeLazyLoader;

    public UserEntityMapper(AddressEntityMapper addressEntityMapper, SafeLazyLoader safeLazyLoader) {
        this.addressEntityMapper = addressEntityMapper;
        this.safeLazyLoader = safeLazyLoader;
    }

    public User toDomain(UserJpaEntity entity) {
        List<Address> addresses = List.of();
        if (safeLazyLoader.isLoaded(entity, "addresses")) {
            addresses = entity.getAddresses().stream().map(addressEntityMapper::toDomain).collect(Collectors.toList());
        }

        return UserBuilder.anUser()
            .withId(new UserId(entity.getId()))
            .withEmail(new Email(entity.getEmail()))
            .withName(new Name(entity.getName()))
            .withBirthday(entity.getBirthday())
            .withAddresses(addresses)
            .build();
    }

    public UserJpaEntity toEntity(User user) {

        UserJpaEntity entity = new UserJpaEntity();
        if (user.getId() != null) {
            entity.setId(user.getId().getValue());
        }
        entity.setEmail(user.getEmail().getValue());
        entity.setBirthday(user.getBirthday());
        entity.setName(user.getName().getValue());

        List<AddressJpaEntity> addresses = user.getAddresses().stream().map(addressEntityMapper::toEntity).toList();
        entity.setAddresses(addresses);

        return entity;
    }

}
