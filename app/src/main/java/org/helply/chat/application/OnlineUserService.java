package org.helply.chat.application;

import org.helply.user.domain.UserId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service to track online users and their WebSocket sessions
 * This is a simplified in-memory implementation that can be extended
 * to use Redis or database for distributed systems
 */
@Service
public class OnlineUserService {
    private static final Logger logger = LoggerFactory.getLogger(OnlineUserService.class);
    
    private final SimpMessagingTemplate messagingTemplate;
    
    // In-memory storage of online users
    // In production, this should be replaced with Redis or database
    private final Set<UserId> onlineUsers = ConcurrentHashMap.newKeySet();
    
    public OnlineUserService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * Mark a user as online
     * @param userId the user ID
     */
    public void markUserOnline(UserId userId) {
        onlineUsers.add(userId);
    }
    
    /**
     * Mark a user as offline
     * @param userId the user ID
     */
    public void markUserOffline(UserId userId) {
        onlineUsers.remove(userId);
    }
    
    /**
     * Check if a user is online
     * @param userId the user ID
     * @return true if the user is online
     */
    public boolean isUserOnline(UserId userId) {
        return onlineUsers.contains(userId);
    }
    
    /**
     * Send a message to a specific user if they are online
     * @param userId the target user ID
     * @param destination the WebSocket destination
     * @param message the message to send
     * @return true if the message was sent (user is online)
     */
    public boolean sendMessageToUser(UserId userId, String destination, Object message) {
        if (isUserOnline(userId)) {
            // Send to user-specific destination
            try {
                messagingTemplate.convertAndSendToUser(
                    userId.getValue().toString(),
                    destination,
                    message
                );
                return true;
            } catch (MessagingException e) {
                logger.error("An error occurred when sending message to user", e);
                return false;
            }
        }
        return false;
    }
    
    /**
     * Get all online users
     * @return set of online user IDs
     */
    public Set<UserId> getOnlineUsers() {
        return Set.copyOf(onlineUsers);
    }
}
