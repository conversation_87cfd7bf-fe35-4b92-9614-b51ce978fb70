package org.helply.chat.application;

import org.helply.chat.application.command.CreateMessageCommand;
import org.helply.chat.application.query.ChatMessageDto;
import org.helply.chat.domain.*;
import org.helply.user.domain.UserId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Set;

@Service
public class ChatService {

    private static final Logger logger = LoggerFactory.getLogger(ChatService.class);

    private final ChatRepository chatRepository;
    private final OnlineUserService onlineUserService;

    public ChatService(ChatRepository chatRepository,
                      OnlineUserService onlineUserService) {
        this.chatRepository = chatRepository;
        this.onlineUserService = onlineUserService;
    }

    public void createMessage(CreateMessageCommand command) {
        logger.info("Creating message for conversation {} from sender {}",
                   command.getConversationId(), command.getSenderId());

        try {
            // 1. Save the chat message
            ChatMessage message = command.toDomain();
            ChatMessage savedMessage = chatRepository.saveMessage(message);
            logger.info("Saved message with ID: {}", savedMessage.getId().getValue());

            // 2. Get chat participants
            ChatId chatId = ChatId.of(command.getConversationId());
            Chat chat = chatRepository.findById(chatId)
                .orElseThrow(() -> new IllegalArgumentException("Chat not found: " + chatId.getValue()));

            Set<UserId> participants = chat.getParticipantIds();
            logger.info("Found {} participants for chat {}", participants.size(), chatId.getValue());

            // 3. Create message DTO for WebSocket transmission
            ChatMessageDto messageDto = ChatMessageDto.fromDomain(savedMessage);

            // 4. Send message to all participants and track delivery status
            for (UserId participantId : participants) {
                // Skip sending to the sender
                if (participantId.equals(UserId.of(command.getSenderId()))) {
                    continue;
                }

                // Check if participant is online and send message
                boolean delivered = onlineUserService.sendMessageToUser(
                    participantId,
                    "/queue/messages",
                    messageDto
                );

                // Track delivery status
                DeliveryStatus status = delivered ? DeliveryStatus.DELIVERED : DeliveryStatus.PENDING;
                MessageDeliveryStatus deliveryStatus = new MessageDeliveryStatus(
                    null, // ID will be generated by repository
                    savedMessage.getId(),
                    participantId,
                    status,
                    LocalDateTime.now()
                );

                chatRepository.saveDeliveryStatus(deliveryStatus);

                logger.info("Message delivery status for user {}: {}",
                           participantId.getValue(), status);
            }

            logger.info("Successfully processed message creation for conversation {}",
                       command.getConversationId());

        } catch (Exception e) {
            logger.error("Error creating message for conversation {}: {}",
                        command.getConversationId(), e.getMessage(), e);
            throw new RuntimeException("Failed to create message", e);
        }
    }
}
