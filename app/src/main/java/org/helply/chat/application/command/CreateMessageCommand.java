package org.helply.chat.application.command;

import org.helply.chat.domain.*;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public class CreateMessageCommand {
    private Long conversationId;
    private Long senderId;
    private String content;
    private MessageType type;
    
    public ChatMessage toDomain() {
        return ChatMessageBuilder.aChatMessage()
            .withConversationId(ChatId.of(conversationId))
            .withSenderId(UserId.of(senderId))
            .withType(type)
            .withContent(MessageContent.of(content))
            .withCreateTime(LocalDateTime.now())
            .build();
    }

    public Long getConversationId() {
        return conversationId;
    }

    public void setConversationId(Long conversationId) {
        this.conversationId = conversationId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public MessageType getType() {
        return type;
    }

    public void setType(MessageType type) {
        this.type = type;
    }
}
