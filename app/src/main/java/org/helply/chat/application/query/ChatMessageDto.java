package org.helply.chat.application.query;

import org.helply.chat.domain.ChatMessage;
import org.helply.chat.domain.MessageType;

import java.time.LocalDateTime;

/**
 * DTO for chat messages sent via WebSocket
 */
public class ChatMessageDto {
    
    private Long id;
    private Long conversationId;
    private Long senderId;
    private String content;
    private MessageType type;
    private LocalDateTime createTime;
    
    public ChatMessageDto() {
    }
    
    public ChatMessageDto(Long id, Long conversationId, Long senderId, 
                         String content, MessageType type, LocalDateTime createTime) {
        this.id = id;
        this.conversationId = conversationId;
        this.senderId = senderId;
        this.content = content;
        this.type = type;
        this.createTime = createTime;
    }
    
    /**
     * Create DTO from domain entity
     */
    public static ChatMessageDto fromDomain(ChatMessage message) {
        return new ChatMessageDto(
            message.getId() != null ? message.getId().getValue() : null,
            message.getConversationId().getValue(),
            message.getSenderId().getValue(),
            message.getContent().getValue(),
            message.getType(),
            message.getCreateTime()
        );
    }
    
    // Getters and setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getConversationId() {
        return conversationId;
    }
    
    public void setConversationId(Long conversationId) {
        this.conversationId = conversationId;
    }
    
    public Long getSenderId() {
        return senderId;
    }
    
    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public MessageType getType() {
        return type;
    }
    
    public void setType(MessageType type) {
        this.type = type;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
