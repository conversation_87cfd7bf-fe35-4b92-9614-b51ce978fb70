package org.helply.chat.infrastructure.persistence.mapper;

import org.helply.chat.domain.Chat;
import org.helply.chat.domain.ChatMessage;
import org.helply.chat.domain.ChatUser;
import org.helply.chat.infrastructure.persistence.jpa.ChatJpaEntity;
import org.helply.chat.infrastructure.persistence.jpa.ChatMessageJpaEntity;
import org.helply.chat.infrastructure.persistence.jpa.ChatUserJpaEntity;

public class ChatEntityMapper {
    public ChatJpaEntity toEntity(Chat chat) {
        
    }
    
    public ChatMessageJpaEntity toEntity(ChatMessage chatMessage) {
    }
    
    public ChatUserJpaEntity toEntity(ChatUser chatUser) {

    }
}
