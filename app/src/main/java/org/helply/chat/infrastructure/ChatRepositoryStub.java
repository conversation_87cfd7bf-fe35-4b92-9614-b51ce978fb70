package org.helply.chat.infrastructure;

import org.helply.chat.domain.*;
import org.helply.user.domain.UserId;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Stub implementation of ChatRepository for testing purposes
 * This should be replaced with a proper JPA implementation
 */
@Repository
public class ChatRepositoryStub implements ChatRepository {
    
    private final Map<Long, ChatMessage> messages = new HashMap<>();
    private final Map<Long, Chat> chats = new HashMap<>();
    private final Map<Long, MessageDeliveryStatus> deliveryStatuses = new HashMap<>();
    
    private final AtomicLong messageIdGenerator = new AtomicLong(1);
    private final AtomicLong deliveryStatusIdGenerator = new AtomicLong(1);
    
    public ChatRepositoryStub() {
        // Initialize with some test data
        initializeTestData();
    }
    
    @Override
    public ChatMessage saveMessage(ChatMessage message) {
        // Generate ID if not present
        ChatMessageId id = message.getId();
        if (id == null) {
            id = ChatMessageId.of(messageIdGenerator.getAndIncrement());
        }
        
        // Create new message with generated ID
        ChatMessage savedMessage = new ChatMessage(
            id,
            message.getConversationId(),
            message.getSenderId(),
            message.getContent(),
            message.getType(),
            message.getCreateTime()
        );
        
        messages.put(id.getValue(), savedMessage);
        return savedMessage;
    }
    
    @Override
    public Optional<Chat> findById(ChatId chatId) {
        return Optional.ofNullable(chats.get(chatId.getValue()));
    }
    
    @Override
    public MessageDeliveryStatus saveDeliveryStatus(MessageDeliveryStatus deliveryStatus) {
        // Generate ID if not present
        MessageDeliveryStatusId id = deliveryStatus.getId();
        if (id == null) {
            id = MessageDeliveryStatusId.of(deliveryStatusIdGenerator.getAndIncrement());
        }
        
        // Create new delivery status with generated ID
        MessageDeliveryStatus savedStatus = new MessageDeliveryStatus(
            id,
            deliveryStatus.getMessageId(),
            deliveryStatus.getRecipientId(),
            deliveryStatus.getStatus(),
            deliveryStatus.getTimestamp()
        );
        
        deliveryStatuses.put(id.getValue(), savedStatus);
        return savedStatus;
    }
    
    private void initializeTestData() {
        // Create a test chat with participants
        ChatId chatId = ChatId.of(1L);
        Set<UserId> participants = Set.of(
            UserId.of(1L),  // User 1
            UserId.of(2L)   // User 2
        );
        
        Chat testChat = new Chat(
            chatId,
            participants,
            LocalDateTime.now().minusDays(1),
            LocalDateTime.now()
        );
        
        chats.put(chatId.getValue(), testChat);
    }
}
