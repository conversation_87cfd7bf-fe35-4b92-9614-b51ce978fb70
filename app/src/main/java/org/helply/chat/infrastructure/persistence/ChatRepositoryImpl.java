package org.helply.chat.infrastructure.persistence;

import org.helply.chat.domain.*;
import org.helply.chat.infrastructure.persistence.jpa.ChatJpaRepository;
import org.helply.chat.infrastructure.persistence.jpa.ChatMessageJpaRepository;
import org.helply.chat.infrastructure.persistence.jpa.ChatUserJpaRepository;

import java.util.Optional;

public class ChatRepositoryImpl implements ChatRepository {
    private final ChatJpaRepository chatJpaRepository;
    private final ChatMessageJpaRepository chatMessageJpaRepository;
    private final ChatUserJpaRepository chatUserJpaRepository;

    public ChatRepositoryImpl(ChatJpaRepository chatJpaRepository, ChatMessageJpaRepository chatMessageJpaRepository, ChatUserJpaRepository chatUserJpaRepository) {
        this.chatJpaRepository = chatJpaRepository;
        this.chatMessageJpaRepository = chatMessageJpaRepository;
        this.chatUserJpaRepository = chatUserJpaRepository;
    }

    @Override
    public Chat saveChat(Chat chat) {
        return null;
    }

    @Override
    public ChatMessage saveMessage(ChatMessage message) {
        return null;
    }

    @Override
    public Optional<Chat> findById(ChatId chatId) {
        return Optional.empty();
    }

    @Override
    public MessageDeliveryStatus saveDeliveryStatus(MessageDeliveryStatus deliveryStatus) {
        return null;
    }
}
