package org.helply.chat.infrastructure.persistence.jpa;

import jakarta.persistence.*;
import org.helply.chat.domain.ChatUserId;

import java.time.LocalDateTime;

@Entity
@IdClass(ChatUserId.class)
public class ChatUserJpaEntity {
    @Id
    private Long userId;
    @Id
    private Long chatId;
    
    private LocalDateTime lastFetchAt;
    


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getChatId() {
        return chatId;
    }

    public void setChatId(Long chatId) {
        this.chatId = chatId;
    }

    public LocalDateTime getLastFetchAt() {
        return lastFetchAt;
    }

    public void setLastFetchAt(LocalDateTime lastFetchAt) {
        this.lastFetchAt = lastFetchAt;
    }
}
