package org.helply.chat.domain;

import org.helply.user.domain.UserId;

import java.time.LocalDateTime;
import java.util.Set;

public final class ChatBuilder {
    private ChatId id;
    private Set<UserId> participantIds;
    private LocalDateTime createTime;
    private LocalDateTime lastMessageTime;

    private ChatBuilder() {
    }

    public static ChatBuilder aChat() {
        return new ChatBuilder();
    }

    public ChatBuilder withId(ChatId id) {
        this.id = id;
        return this;
    }

    public ChatBuilder withParticipantIds(Set<UserId> participantIds) {
        this.participantIds = participantIds;
        return this;
    }

    public ChatBuilder withCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public ChatBuilder withLastMessageTime(LocalDateTime lastMessageTime) {
        this.lastMessageTime = lastMessageTime;
        return this;
    }

    public Chat build() {
        return new Chat(id, participantIds, createTime, lastMessageTime);
    }
}
