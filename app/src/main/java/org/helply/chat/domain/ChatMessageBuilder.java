package org.helply.chat.domain;

import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public final class ChatMessageBuilder {
    private ChatMessageId id;
    private ChatId chatId;
    private UserId senderId;
    private MessageContent content;
    private MessageType type;
    private LocalDateTime createTime;

    private ChatMessageBuilder() {
    }

    public static ChatMessageBuilder aChatMessage() {
        return new ChatMessageBuilder();
    }

    public ChatMessageBuilder withId(ChatMessageId id) {
        this.id = id;
        return this;
    }

    public ChatMessageBuilder withConversationId(ChatId chatId) {
        this.chatId = chatId;
        return this;
    }

    public ChatMessageBuilder withSenderId(UserId senderId) {
        this.senderId = senderId;
        return this;
    }

    public ChatMessageBuilder withContent(MessageContent content) {
        this.content = content;
        return this;
    }

    public ChatMessageBuilder withType(MessageType type) {
        this.type = type;
        return this;
    }

    public ChatMessageBuilder withCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public ChatMessage build() {
        return new ChatMessage(id, chatId, senderId, content, type, createTime);
    }
}
