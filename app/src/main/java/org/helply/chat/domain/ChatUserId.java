package org.helply.chat.domain;

import org.helply.shared.domain.ValueObject;

import java.util.Objects;

public class ChatUserId extends ValueObject {
    private final Long chatId;
    private final Long userId;

    public ChatUserId(Long chatId, Long userId) {
        this.chatId = chatId;
        this.userId = userId;
        validate();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ChatUserId)) return false;
        ChatUserId that = (ChatUserId) o;
        return Objects.equals(chatId, that.chatId) && Objects.equals(userId, that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(chatId, userId);
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[]{chatId, userId};
    }

    public Long getChatId() {
        return chatId;
    }

    public Long getUserId() {
        return userId;
    }

    @Override
    protected void validate() {
        Objects.requireNonNull(this.chatId);
        Objects.requireNonNull(this.userId);
    }
}
