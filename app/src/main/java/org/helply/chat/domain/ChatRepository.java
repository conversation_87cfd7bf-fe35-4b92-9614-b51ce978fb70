package org.helply.chat.domain;

import java.util.Optional;

public interface ChatRepository {

    /**
     * Save a chat message to the repository
     * @param message the chat message to save
     * @return the saved chat message with generated ID
     */
    ChatMessage saveMessage(ChatMessage message);

    /**
     * Find a chat by its ID
     * @param chatId the chat ID
     * @return the chat if found
     */
    Optional<Chat> findById(ChatId chatId);

    /**
     * Save message delivery status
     * @param deliveryStatus the delivery status to save
     * @return the saved delivery status with generated ID
     */
    MessageDeliveryStatus saveDeliveryStatus(MessageDeliveryStatus deliveryStatus);
}
