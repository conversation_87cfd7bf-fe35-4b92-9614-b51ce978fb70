package org.helply.chat.domain;

import org.helply.shared.domain.DomainEntity;

import java.time.LocalDateTime;

public class Chat<PERSON>ser extends DomainEntity<ChatUserId> {
    private ChatUserId id;
    private LocalDateTime lastFetchTime;

    public ChatUser(ChatUserId id, LocalDateTime lastFetchTime) {
        this.id = id;
        this.lastFetchTime = lastFetchTime;
    }

    @Override
    public ChatUserId getId() {
        return id;
    }

    public LocalDateTime getLastFetchTime() {
        return lastFetchTime;
    }
}
