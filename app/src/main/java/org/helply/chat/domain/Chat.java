package org.helply.chat.domain;

import org.helply.shared.domain.AggregateRoot;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

public class Chat extends AggregateRoot<ChatId> {
    private ChatId id;
    private Map<UserId, ChatUser> participants;
    private LocalDateTime createTime;
    private LocalDateTime lastMessageTime;
    private Set<ChatMessage> messages;
    
    public Chat(ChatId id, Map<UserId, ChatUser> participants, Set<ChatMessage> messages, LocalDateTime createTime, LocalDateTime lastMessageTime) {
        this.id = id;
        this.participants = participants;
        this.createTime = createTime;
        this.messages = messages;
        this.lastMessageTime = lastMessageTime;
    }

    @Override
    public ChatId getId() {
        return id;
    }

    public Map<UserId, ChatUser> getParticipants() {
        return participants;
    }

    public Set<ChatMessage> getMessages() {
        return messages;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public LocalDateTime getLastMessageTime() {
        return lastMessageTime;
    }
}
