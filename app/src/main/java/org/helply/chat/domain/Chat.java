package org.helply.chat.domain;

import org.helply.shared.domain.AggregateRoot;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;
import java.util.Set;

public class Chat extends AggregateRoot<ChatId> {
    private ChatId id;
    private Set<UserId> participantIds;
    private LocalDateTime createTime;
    private LocalDateTime lastMessageTime;
    
    public Chat(ChatId id, Set<UserId> participantIds, LocalDateTime createTime, LocalDateTime lastMessageTime) {
        this.id = id;
        this.participantIds = participantIds;
        this.createTime = createTime;
        this.lastMessageTime = lastMessageTime;
    }

    @Override
    public ChatId getId() {
        return id;
    }

    public Set<UserId> getParticipantIds() {
        return participantIds;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public LocalDateTime getLastMessageTime() {
        return lastMessageTime;
    }
}
