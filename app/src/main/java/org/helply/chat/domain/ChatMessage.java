package org.helply.chat.domain;

import org.helply.shared.domain.DomainEntity;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

public class ChatMessage extends DomainEntity<ChatMessageId> {
    private final ChatMessageId id;
    private ChatId chatId;
    private UserId senderId;
    private MessageContent content;
    private MessageType type;
    private LocalDateTime createTime;

    public ChatMessage(ChatMessageId id, ChatId chatId, UserId senderId, MessageContent content, MessageType type, LocalDateTime createTime) {
        this.id = id;
        this.chatId = chatId;
        this.senderId = senderId;
        this.content = content;
        this.type = type;
        this.createTime = createTime;
    }

    @Override
    public ChatMessageId getId() {
        return id;
    }

    public ChatId getConversationId() {
        return chatId;
    }

    public UserId getSenderId() {
        return senderId;
    }

    public MessageContent getContent() {
        return content;
    }

    public MessageType getType() {
        return type;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }
}
