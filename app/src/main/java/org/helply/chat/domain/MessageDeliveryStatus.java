package org.helply.chat.domain;

import org.helply.shared.domain.DomainEntity;
import org.helply.user.domain.UserId;

import java.time.LocalDateTime;

/**
 * Domain entity to track message delivery status for each participant
 */
public class MessageDeliveryStatus extends DomainEntity<MessageDeliveryStatusId> {
    
    private final MessageDeliveryStatusId id;
    private final ChatMessageId messageId;
    private final UserId recipientId;
    private final DeliveryStatus status;
    private final LocalDateTime timestamp;
    
    public MessageDeliveryStatus(MessageDeliveryStatusId id, ChatMessageId messageId, 
                               UserId recipientId, DeliveryStatus status, LocalDateTime timestamp) {
        this.id = id;
        this.messageId = messageId;
        this.recipientId = recipientId;
        this.status = status;
        this.timestamp = timestamp;
    }
    
    @Override
    public MessageDeliveryStatusId getId() {
        return id;
    }
    
    public ChatMessageId getMessageId() {
        return messageId;
    }
    
    public UserId getRecipientId() {
        return recipientId;
    }
    
    public DeliveryStatus getStatus() {
        return status;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
}
