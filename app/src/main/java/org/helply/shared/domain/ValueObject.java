package org.helply.shared.domain;

import java.io.Serializable;
import java.util.Arrays;

public abstract class ValueObject implements Serializable {

    /**
     * Template method for equality comparison.
     * Subclasses should implement getEqualityComponents() to define
     * which fields participate in equality.
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        ValueObject that = (ValueObject) obj;
        return Arrays.deepEquals(this.getEqualityComponents(), that.getEqualityComponents());
    }

    @Override
    public int hashCode() {
        return Arrays.deepHashCode(getEqualityComponents());
    }

    @Override
    public String toString() {
        return String.format("%s{%s}",
            getClass().getSimpleName(),
            Arrays.deepToString(getEqualityComponents()));
    }

    /**
     * Returns the components that should be used for equality comparison.
     * Subclasses must implement this method.
     */
    protected abstract Object[] getEqualityComponents();

    /**
     * Validation method called during construction.
     * Subclasses should override to add validation logic.
     */
    protected void validate() {
        // Default implementation - no validation
    }
}