package org.helply.shared.domain;

import org.apache.commons.lang3.StringUtils;

public class PhoneNumber extends ValueObject {

    private final String countryCode;
    private final String phoneNumber;

    public PhoneNumber(String countryCode, String phoneNumber) {
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        validate();
    }

    public String getCountryCode() {
        return countryCode;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }
    
    public static PhoneNumber of(String countryCode, String phoneNumber) {
        return new PhoneNumber(countryCode, phoneNumber);
    }

    @Override
    protected void validate() {
        if (StringUtils.isBlank(countryCode)) {
            throw new IllegalArgumentException("country code cannot be empty!");
        }
        
        if (StringUtils.isBlank(phoneNumber)) {
            throw new IllegalArgumentException("phone number cannot be empty!");
        }

        if (countryCode.charAt(0) != '+' && countryCode.length() < 2) {
            throw new IllegalArgumentException("invalid country code value!");
        }
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] { countryCode, phoneNumber };
    }
}
