package org.helply.shared.domain;

import java.util.Objects;

public abstract class DomainEntity<ID> {

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DomainEntity<?> domainEntity = (DomainEntity<?>) obj;
        return getId() != null && Objects.equals(getId(), domainEntity.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }

    @Override
    public String toString() {
        return String.format("%s{id=%s}", getClass().getSimpleName(), getId());
    }

    /**
     * Returns the unique identifier of this domain entity.
     * Subclasses must implement this method.
     */
    public abstract ID getId();

    /**
     * Checks if this domain entity has an identity (non-null ID).
     */
    public boolean hasIdentity() {
        return getId() != null;
    }

    /**
     * Checks if this domain entity is the same as another domain entity.
     * Two entities are considered the same if they have the same ID and type.
     */
    public boolean sameIdentityAs(DomainEntity<ID> other) {
        return other != null &&
            Objects.equals(getId(), other.getId()) &&
            getClass().equals(other.getClass());
    }
}
