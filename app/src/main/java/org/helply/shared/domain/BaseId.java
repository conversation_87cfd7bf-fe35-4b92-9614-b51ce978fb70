package org.helply.shared.domain;

public abstract class BaseId extends ValueObject {
    private final Long value;

    public BaseId(Long value) {
        this.value = value;
        validate();
    }

    public Long getValue() {
        return value;
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] { value };
    }

    @Override
    protected void validate() {
        if (value == null || value < 0) {
            throw new IllegalArgumentException("value cannot be negative!");
        }
    }
}
