package org.helply.shared.domain;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public abstract class AggregateRoot<ID> extends DomainEntity<ID> {
    private final List<DomainEvent> domainEvents = new ArrayList<>();
    private long version = 0;

    /**
     * Adds a domain event to be published when the aggregate is saved.
     */
    protected void addDomainEvent(DomainEvent event) {
        if (event != null) {
            domainEvents.add(event);
        }
    }

    /**
     * Removes a specific domain event.
     */
    protected void removeDomainEvent(DomainEvent event) {
        domainEvents.remove(event);
    }

    /**
     * Clears all domain events.
     */
    public void clearDomainEvents() {
        domainEvents.clear();
    }

    /**
     * Returns all domain events. Should be called by repository after saving.
     */
    public List<DomainEvent> getDomainEvents() {
        return Collections.unmodifiableList(domainEvents);
    }

    /**
     * Checks if there are any unpublished domain events.
     */
    public boolean hasUnpublishedEvents() {
        return !domainEvents.isEmpty();
    }

    /**
     * Gets the version for optimistic locking.
     */
    public long getVersion() {
        return version;
    }

    /**
     * Sets the version. Should only be called by infrastructure (ORM).
     */
    public void setVersion(long version) {
        this.version = version;
    }

    /**
     * Increments the version. Called when aggregate is modified.
     */
    protected void incrementVersion() {
        this.version++;
    }

    /**
     * Template method for aggregate validation.
     * Subclasses can override to add validation logic.
     */
    protected void validate() {
        // Default implementation - no validation
    }

    /**
     * Marks the aggregate as modified by incrementing version.
     * Call this method after any state change.
     */
    protected void markAsModified() {
        incrementVersion();
    }
}