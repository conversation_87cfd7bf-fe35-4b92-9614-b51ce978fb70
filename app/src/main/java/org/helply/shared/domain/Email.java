package org.helply.shared.domain;

import java.util.Objects;
import java.util.regex.Pattern;

public class Email extends ValueObject {
    private final String value;
    private static final Pattern emailPattern = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\." +
            "[a-zA-Z0-9_+&*-]+)*@" +
            "(?:[a-zA-Z0-9-]+\\.)+[a-z" +
            "A-Z]{2,7}$");

    public Email(String value) {
        this.value = value;
        validate();
    }

    public String getValue() {
        return value;
    }

    public static Email of(String value) {
        return new Email(value);
    }

    @Override
    protected Object[] getEqualityComponents() {
        return new Object[] {value};
    }

    @Override
    protected void validate() {
        Objects.requireNonNull(value);
        if (!emailPattern.asMatchPredicate().test(value)) {
            throw new IllegalArgumentException("Invalid email address: " + value);
        }
    }
}
