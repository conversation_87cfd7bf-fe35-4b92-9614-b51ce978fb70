package org.helply.shared.infrastructure.util;

import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.PersistenceUnitUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Supplier;

@Component
public class SafeLazyLoader {

    private final PersistenceUnitUtil persistenceUnitUtil;

    public SafeLazyLoader(EntityManagerFactory entityManagerFactory) {
        this.persistenceUnitUtil = entityManagerFactory.getPersistenceUnitUtil();
    }

    public boolean isLoaded(Object entity, String attributeName) {
        return persistenceUnitUtil.isLoaded(entity, attributeName);
    }

    public <R> Optional<R> ifLoaded(Object entity, String attributeName, Supplier<R> getter) {
        return persistenceUnitUtil.isLoaded(entity, attributeName) ? Optional.of(getter.get()) : Optional.empty();
    }
}