package org.helply.shared.infrastructure;

import org.helply.account.domain.AuthProvider;
import org.helply.account.infrastructure.persistence.jpa.AccountJpaEntity;
import org.helply.account.infrastructure.persistence.jpa.AccountJpaRepository;
import org.helply.task.domain.TaskCategory;
import org.helply.task.infrastructure.persistence.jpa.TaskJpaEntity;
import org.helply.task.infrastructure.persistence.jpa.TaskJpaRepository;
import org.helply.user.infrastructure.persistence.jpa.UserJpaEntity;
import org.helply.user.infrastructure.persistence.jpa.UserJpaRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
public class DataInit implements CommandLineRunner {
    private final UserJpaRepository userJpaRepository;
    private final AccountJpaRepository accountJpaRepository;
    private final TaskJpaRepository taskJpaRepository;

    public DataInit(UserJpaRepository userJpaRepository, AccountJpaRepository accountJpaRepository, TaskJpaRepository taskJpaRepository) {
        this.userJpaRepository = userJpaRepository;
        this.accountJpaRepository = accountJpaRepository;
        this.taskJpaRepository = taskJpaRepository;
    }

    @Override
    public void run(String... args) throws Exception {
        LocalDateTime now = LocalDateTime.now();

        // Create user
        UserJpaEntity user1 = new UserJpaEntity();
        user1.setName("Feng Lin");
        user1.setBirthday(now.minusYears(30).toLocalDate());
        user1.setEmail("<EMAIL>");
        userJpaRepository.save(user1);

        // Create account
        AccountJpaEntity account1 = new AccountJpaEntity();
        account1.setEmail(user1.getEmail());
        account1.setProvider(AuthProvider.LOCAL);
        account1.setPassword("nAC+6aTpl24s4D3cVUwQZw==$9W3efxoaCmZN0d1EIaTgsOtbRrPxTNwvClLgfEk9vz4=");
        account1.setUserId(user1.getId());
        accountJpaRepository.save(account1);

        // Generate 20 diverse task entities
        generateTasks(user1.getId());
    }

    private void generateTasks(Long userId) {
        // Task 1: Pet Care
        TaskJpaEntity task1 = new TaskJpaEntity();
        task1.setTitle("Dog Walking Service");
        task1.setDescription("Professional dog walking service for busy pet owners. I will walk your dog for 30-60 minutes, ensuring they get proper exercise and attention. Available weekdays and weekends.");
        task1.setCategory(TaskCategory.PET_CARE);
        task1.setPrice(new BigDecimal("25.00"));
        task1.setUserId(userId);
        taskJpaRepository.save(task1);

        // Task 2: Driving
        TaskJpaEntity task2 = new TaskJpaEntity();
        task2.setTitle("Airport Pickup Service");
        task2.setDescription("Reliable airport pickup and drop-off service. Clean vehicle, punctual service, and help with luggage. Available 24/7 with advance booking.");
        task2.setCategory(TaskCategory.DRIVING);
        task2.setPrice(new BigDecimal("45.00"));
        task2.setUserId(userId);
        taskJpaRepository.save(task2);

        // Task 3: Errand
        TaskJpaEntity task3 = new TaskJpaEntity();
        task3.setTitle("Grocery Shopping Assistant");
        task3.setDescription("I'll do your grocery shopping for you! Provide me with your list and I'll carefully select fresh items and deliver them to your door. Perfect for busy professionals or elderly clients.");
        task3.setCategory(TaskCategory.ERRAND);
        task3.setPrice(new BigDecimal("20.00"));
        task3.setUserId(userId);
        taskJpaRepository.save(task3);

        // Task 4: Photography
        TaskJpaEntity task4 = new TaskJpaEntity();
        task4.setTitle("Portrait Photography Session");
        task4.setDescription("Professional portrait photography for individuals, couples, or families. Includes 1-hour session, basic editing, and 20 high-resolution digital images. Studio or outdoor location available.");
        task4.setCategory(TaskCategory.PHOTOGRAPHY);
        task4.setPrice(new BigDecimal("150.00"));
        task4.setUserId(userId);
        taskJpaRepository.save(task4);

        // Task 5: Design
        TaskJpaEntity task5 = new TaskJpaEntity();
        task5.setTitle("Logo Design Service");
        task5.setDescription("Custom logo design for your business or personal brand. Includes 3 initial concepts, 2 rounds of revisions, and final files in multiple formats (PNG, SVG, PDF).");
        task5.setCategory(TaskCategory.DESIGN);
        task5.setPrice(new BigDecimal("200.00"));
        task5.setUserId(userId);
        taskJpaRepository.save(task5);

        // Task 6: Writing
        TaskJpaEntity task6 = new TaskJpaEntity();
        task6.setTitle("Content Writing for Blogs");
        task6.setDescription("Professional blog post writing service. SEO-optimized content, well-researched articles, and engaging writing style. 500-1500 words per article with unlimited revisions.");
        task6.setCategory(TaskCategory.WRITING);
        task6.setPrice(new BigDecimal("75.00"));
        task6.setUserId(userId);
        taskJpaRepository.save(task6);

        // Task 7: Translation
        TaskJpaEntity task7 = new TaskJpaEntity();
        task7.setTitle("English to Spanish Translation");
        task7.setDescription("Native Spanish speaker offering professional translation services. Documents, websites, marketing materials, and more. Quick turnaround and accurate translations guaranteed.");
        task7.setCategory(TaskCategory.TRANSLATION);
        task7.setPrice(new BigDecimal("0.15"));
        task7.setUserId(userId);
        taskJpaRepository.save(task7);

        // Task 8: Remote Assistance
        TaskJpaEntity task8 = new TaskJpaEntity();
        task8.setTitle("Virtual Assistant Services");
        task8.setDescription("Comprehensive virtual assistant services including email management, appointment scheduling, data entry, and administrative tasks. Available during business hours EST.");
        task8.setCategory(TaskCategory.REMOTE_ASSISTANCE);
        task8.setPrice(new BigDecimal("30.00"));
        task8.setUserId(userId);
        taskJpaRepository.save(task8);

        // Task 9: Pet Care
        TaskJpaEntity task9 = new TaskJpaEntity();
        task9.setTitle("Pet Sitting Overnight");
        task9.setDescription("Overnight pet sitting in your home. Your pets stay comfortable in their familiar environment while you're away. Includes feeding, walks, playtime, and lots of love and attention.");
        task9.setCategory(TaskCategory.PET_CARE);
        task9.setPrice(new BigDecimal("60.00"));
        task9.setUserId(userId);
        taskJpaRepository.save(task9);

        // Task 10: Driving
        TaskJpaEntity task10 = new TaskJpaEntity();
        task10.setTitle("Moving Day Assistance");
        task10.setDescription("Help with your moving day! I have a pickup truck and can assist with transporting furniture and boxes. Careful handling and efficient service guaranteed.");
        task10.setCategory(TaskCategory.DRIVING);
        task10.setPrice(new BigDecimal("80.00"));
        task10.setUserId(userId);
        taskJpaRepository.save(task10);

        // Task 11: Errand
        TaskJpaEntity task11 = new TaskJpaEntity();
        task11.setTitle("Package Delivery Service");
        task11.setDescription("Local package delivery and pickup service. Same-day delivery within the city. Perfect for urgent documents, gifts, or items that need personal handling.");
        task11.setCategory(TaskCategory.ERRAND);
        task11.setPrice(new BigDecimal("15.00"));
        task11.setUserId(userId);
        taskJpaRepository.save(task11);

        // Task 12: Photography
        TaskJpaEntity task12 = new TaskJpaEntity();
        task12.setTitle("Event Photography");
        task12.setDescription("Professional event photography for weddings, parties, corporate events, and special occasions. Full-day coverage with edited photos delivered within 2 weeks.");
        task12.setCategory(TaskCategory.PHOTOGRAPHY);
        task12.setPrice(new BigDecimal("500.00"));
        task12.setUserId(userId);
        taskJpaRepository.save(task12);

        // Task 13: Design
        TaskJpaEntity task13 = new TaskJpaEntity();
        task13.setTitle("Website UI/UX Design");
        task13.setDescription("Modern and responsive website design. User-friendly interfaces, mobile optimization, and conversion-focused layouts. Includes wireframes, mockups, and design system.");
        task13.setCategory(TaskCategory.DESIGN);
        task13.setPrice(new BigDecimal("800.00"));
        task13.setUserId(userId);
        taskJpaRepository.save(task13);

        // Task 14: Writing
        TaskJpaEntity task14 = new TaskJpaEntity();
        task14.setTitle("Resume Writing Service");
        task14.setDescription("Professional resume writing and optimization. ATS-friendly formats, keyword optimization, and compelling content that highlights your achievements and skills.");
        task14.setCategory(TaskCategory.WRITING);
        task14.setPrice(new BigDecimal("120.00"));
        task14.setUserId(userId);
        taskJpaRepository.save(task14);

        // Task 15: Translation
        TaskJpaEntity task15 = new TaskJpaEntity();
        task15.setTitle("French Document Translation");
        task15.setDescription("Certified French to English translation services. Legal documents, academic transcripts, business contracts, and personal documents. Fast and accurate translations.");
        task15.setCategory(TaskCategory.TRANSLATION);
        task15.setPrice(new BigDecimal("0.20"));
        task15.setUserId(userId);
        taskJpaRepository.save(task15);

        // Task 16: Remote Assistance
        TaskJpaEntity task16 = new TaskJpaEntity();
        task16.setTitle("Social Media Management");
        task16.setDescription("Complete social media management for small businesses. Content creation, posting schedule, engagement with followers, and monthly analytics reports.");
        task16.setCategory(TaskCategory.REMOTE_ASSISTANCE);
        task16.setPrice(new BigDecimal("400.00"));
        task16.setUserId(userId);
        taskJpaRepository.save(task16);

        // Task 17: Other
        TaskJpaEntity task17 = new TaskJpaEntity();
        task17.setTitle("Home Cleaning Service");
        task17.setDescription("Thorough home cleaning service including all rooms, bathrooms, kitchen, and common areas. Eco-friendly products available. Weekly, bi-weekly, or one-time cleaning.");
        task17.setCategory(TaskCategory.OTHER);
        task17.setPrice(new BigDecimal("100.00"));
        task17.setUserId(userId);
        taskJpaRepository.save(task17);

        // Task 18: Other
        TaskJpaEntity task18 = new TaskJpaEntity();
        task18.setTitle("Furniture Assembly");
        task18.setDescription("Professional furniture assembly service. IKEA, Amazon, and other flat-pack furniture. Tools provided, quick assembly, and cleanup included. Same-day service available.");
        task18.setCategory(TaskCategory.OTHER);
        task18.setPrice(new BigDecimal("50.00"));
        task18.setUserId(userId);
        taskJpaRepository.save(task18);

        // Task 19: Other
        TaskJpaEntity task19 = new TaskJpaEntity();
        task19.setTitle("Garden Maintenance");
        task19.setDescription("Complete garden maintenance including lawn mowing, hedge trimming, weeding, and seasonal cleanup. Regular maintenance schedules available for ongoing care.");
        task19.setCategory(TaskCategory.OTHER);
        task19.setPrice(new BigDecimal("75.00"));
        task19.setUserId(userId);
        taskJpaRepository.save(task19);

        // Task 20: Other
        TaskJpaEntity task20 = new TaskJpaEntity();
        task20.setTitle("Personal Tutoring - Math");
        task20.setDescription("Experienced math tutor for high school and college students. Algebra, calculus, statistics, and test preparation. Online or in-person sessions available.");
        task20.setCategory(TaskCategory.OTHER);
        task20.setPrice(new BigDecimal("40.00"));
        task20.setUserId(userId);
        taskJpaRepository.save(task20);
    }
}
