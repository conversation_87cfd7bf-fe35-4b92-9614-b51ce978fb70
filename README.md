# Heply

## Chat

Real-time Communication Strategy: WebSockets
Domain Model: Conversation(conversation ID, participants, created date, last message timestamp), 
Message(message ID, conversation ID, sender ID, content, timestamp, message type)
MessageType: text, image
For images, decide whether to store them as files with references in the database or use a cloud storage service.

Set Up WebSocket Infrastructure

Basic flow:
User A sends message via WebSocket
Spring Boot receives and validates message
Message gets persisted to database
Message gets broadcast to User B via WebSocket
Both clients update their UI

